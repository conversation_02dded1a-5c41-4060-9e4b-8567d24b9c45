<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Pump.fun Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e0e0;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ecdc4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #4ecdc4;
            font-size: 1.2rem;
        }

        .tokens-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .token-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .token-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .token-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .token-name {
            font-weight: bold;
            color: #fff;
        }

        .token-symbol {
            color: #4ecdc4;
            font-size: 0.9rem;
        }

        .price-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }

        .price-item {
            text-align: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .price-label {
            font-size: 0.8rem;
            color: #aaa;
            margin-bottom: 2px;
        }

        .price-value {
            font-weight: bold;
            color: #fff;
        }

        .positive { color: #4ecdc4; }
        .negative { color: #ff6b6b; }

        .trading-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            color: #aaa;
            margin-bottom: 2px;
        }

        .stat-value {
            color: #fff;
            font-weight: bold;
        }

        .dexscreener-link {
            display: inline-block;
            margin-top: 10px;
            padding: 5px 10px;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.8rem;
            transition: opacity 0.2s ease;
        }

        .dexscreener-link:hover {
            opacity: 0.8;
        }

        .update-time {
            text-align: center;
            margin-top: 20px;
            color: #aaa;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #4ecdc4;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .tokens-grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Enhanced Pump.fun Trading Dashboard</h1>
            <p>Real-time DexScreener integration with last 10 trades in 24h</p>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="status-dot"></div>
                <span id="status-text">Connecting to DexScreener...</span>
            </div>
            <div class="update-time">
                Last updated: <span id="last-update">Never</span>
            </div>
        </div>

        <div id="loading" class="loading">
            <div>🔄 Loading enhanced token data from DexScreener...</div>
        </div>

        <div id="dashboard" style="display: none;">
            <div class="dashboard-grid">
                <div class="card">
                    <h3>📊 Trading Summary</h3>
                    <div id="trading-summary">
                        <div class="price-info">
                            <div class="price-item">
                                <div class="price-label">Total Tokens</div>
                                <div class="price-value" id="total-tokens">0</div>
                            </div>
                            <div class="price-item">
                                <div class="price-label">Avg 24h Volume</div>
                                <div class="price-value" id="avg-volume">$0</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🎯 Performance Metrics</h3>
                    <div id="performance-metrics">
                        <div class="price-info">
                            <div class="price-item">
                                <div class="price-label">Avg Price Change</div>
                                <div class="price-value" id="avg-price-change">0%</div>
                            </div>
                            <div class="price-item">
                                <div class="price-label">Total Market Cap</div>
                                <div class="price-value" id="total-market-cap">$0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 Enhanced Token Tracking (DexScreener Data)</h3>
                <div id="tokens-container" class="tokens-grid">
                    <!-- Enhanced tokens will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function fetchEnhancedDashboardData() {
            try {
                const response = await fetch('/api/enhanced-dashboard');
                const data = await response.json();
                updateEnhancedDashboard(data);
            } catch (error) {
                console.error('Error fetching enhanced dashboard data:', error);
                document.getElementById('status-text').textContent = 'Connection Error';
                document.getElementById('status-dot').style.background = '#ff6b6b';
            }
        }

        function updateEnhancedDashboard(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
            
            // Update status
            document.getElementById('status-text').textContent = 'Connected to DexScreener';
            document.getElementById('status-dot').style.background = '#4ecdc4';
            
            // Update summary
            if (data.enhanced_tokens) {
                updateTradingSummary(data.enhanced_tokens);
                updateTokensDisplay(data.enhanced_tokens);
            }
            
            // Update timestamp
            const now = new Date();
            document.getElementById('last-update').textContent = now.toLocaleTimeString();
        }

        function updateTradingSummary(tokens) {
            const totalTokens = tokens.length;
            const avgVolume = tokens.reduce((sum, token) => sum + token.volume_24h, 0) / totalTokens;
            const avgPriceChange = tokens.reduce((sum, token) => sum + token.price_change_24h, 0) / totalTokens;
            const totalMarketCap = tokens.reduce((sum, token) => sum + token.market_cap, 0);
            
            document.getElementById('total-tokens').textContent = totalTokens;
            document.getElementById('avg-volume').textContent = `$${avgVolume.toLocaleString()}`;
            document.getElementById('avg-price-change').textContent = `${avgPriceChange.toFixed(2)}%`;
            document.getElementById('avg-price-change').className = `price-value ${avgPriceChange >= 0 ? 'positive' : 'negative'}`;
            document.getElementById('total-market-cap').textContent = `$${totalMarketCap.toLocaleString()}`;
        }

        function updateTokensDisplay(tokens) {
            const container = document.getElementById('tokens-container');
            container.innerHTML = '';
            
            tokens.forEach(token => {
                const tokenCard = createEnhancedTokenCard(token);
                container.appendChild(tokenCard);
            });
        }

        function createEnhancedTokenCard(token) {
            const card = document.createElement('div');
            card.className = 'token-card';
            
            const priceChangeClass = token.price_change_24h >= 0 ? 'positive' : 'negative';
            const priceChangeIcon = token.price_change_24h >= 0 ? '📈' : '📉';
            
            card.innerHTML = `
                <div class="token-header">
                    <div>
                        <div class="token-name">${token.name}</div>
                        <div class="token-symbol">${token.symbol}</div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 0.8rem; color: #aaa;">24h Change</div>
                        <div class="${priceChangeClass}">${priceChangeIcon} ${token.price_change_24h.toFixed(2)}%</div>
                    </div>
                </div>
                
                <div class="price-info">
                    <div class="price-item">
                        <div class="price-label">Price (USD)</div>
                        <div class="price-value">$${token.price_usd.toFixed(8)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Price (SOL)</div>
                        <div class="price-value">${token.price_sol.toFixed(9)}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Market Cap</div>
                        <div class="price-value">$${token.market_cap.toLocaleString()}</div>
                    </div>
                    <div class="price-item">
                        <div class="price-label">Liquidity</div>
                        <div class="price-value">$${token.liquidity_usd.toLocaleString()}</div>
                    </div>
                </div>
                
                <div class="trading-stats">
                    <div class="stat-item">
                        <div class="stat-label">24h Volume</div>
                        <div class="stat-value">$${token.volume_24h.toLocaleString()}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">24h Trades</div>
                        <div class="stat-value">${token.txns_24h}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Buys/Sells</div>
                        <div class="stat-value">${token.buys_24h}/${token.sells_24h}</div>
                    </div>
                </div>
                
                <a href="https://dexscreener.com/solana/${token.address}" target="_blank" class="dexscreener-link">
                    🔗 View on DexScreener
                </a>
            `;
            
            return card;
        }

        // Start fetching data
        fetchEnhancedDashboardData();
        
        // Refresh every 30 seconds
        setInterval(fetchEnhancedDashboardData, 30000);
    </script>
</body>
</html>
