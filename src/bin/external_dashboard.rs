//! External Trading Dashboard Binary
//!
//! This is a standalone binary that runs the external trading dashboard
//! completely independent of the main trading bot. It fetches data from
//! external sources like DexScreener to display trading activity.

use anyhow::Result;
use std::env;
use tokio::signal;

// Import the external dashboard modules from the library
use blockchain_bot::external_dashboard::{ExternalDashboard, ExternalDashboardConfig};

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 EXTERNAL TRADING DASHBOARD");
    println!("=============================");
    println!("📊 Independent trading activity viewer");
    println!("🌐 Fetches data from DexScreener API");
    println!("🔄 Updates every 30 seconds");
    println!();

    // Get configuration from environment variables or use defaults
    let config = get_dashboard_config();

    // Create and start the dashboard
    let mut dashboard = ExternalDashboard::new(config);

    // Start the dashboard
    match dashboard.start().await {
        Ok(()) => {
            println!("✅ Dashboard started successfully!");
            println!();
            println!("🌐 Access your dashboard at: http://localhost:42069");
            println!("📊 The dashboard will automatically refresh trading data");
            println!("🛑 Press Ctrl+C to stop the dashboard");
            println!();

            // Wait for shutdown signal
            wait_for_shutdown().await;

            // Stop the dashboard
            dashboard.stop().await;
            println!("👋 Dashboard stopped. Goodbye!");
        }
        Err(e) => {
            eprintln!("❌ Failed to start dashboard: {}", e);
            eprintln!();
            eprintln!("💡 Troubleshooting tips:");
            eprintln!("   - Make sure port 42069 is not in use");
            eprintln!("   - Check your internet connection");
            eprintln!("   - Verify the wallet address is correct");
            std::process::exit(1);
        }
    }

    Ok(())
}

/// Get dashboard configuration from environment variables
fn get_dashboard_config() -> ExternalDashboardConfig {
    let wallet_address = env::var("PUMP_WALLET_ADDRESS")
        .or_else(|_| env::var("WALLET_ADDRESS"))
        .unwrap_or_else(|_| {
            println!("⚠️  No wallet address specified in environment variables");
            println!("   Using default wallet address");
            println!("   Set PUMP_WALLET_ADDRESS or WALLET_ADDRESS to customize");
            "GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus".to_string()
        });

    let port = env::var("DASHBOARD_PORT")
        .unwrap_or_else(|_| "42069".to_string())
        .parse()
        .unwrap_or(42069);

    let refresh_interval = env::var("DASHBOARD_REFRESH_INTERVAL")
        .unwrap_or_else(|_| "30".to_string())
        .parse()
        .unwrap_or(30);

    let max_trades = env::var("DASHBOARD_MAX_TRADES")
        .unwrap_or_else(|_| "10".to_string())
        .parse()
        .unwrap_or(10);

    println!("📋 Dashboard Configuration:");
    println!("   Wallet: {}...{}", 
        &wallet_address[..8], 
        &wallet_address[wallet_address.len()-8..]);
    println!("   Port: {}", port);
    println!("   Refresh Interval: {}s", refresh_interval);
    println!("   Max Trades Display: {}", max_trades);
    println!();

    ExternalDashboardConfig {
        wallet_address,
        port,
        refresh_interval_seconds: refresh_interval,
        max_trades_display: max_trades,
    }
}

/// Wait for shutdown signal (Ctrl+C)
async fn wait_for_shutdown() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("Failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            println!("\n🛑 Received Ctrl+C, shutting down...");
        },
        _ = terminate => {
            println!("\n🛑 Received terminate signal, shutting down...");
        },
    }
}

/// Print usage information
fn print_usage() {
    println!("📖 USAGE");
    println!("=========");
    println!();
    println!("Environment Variables:");
    println!("  PUMP_WALLET_ADDRESS    - Your Solana wallet address");
    println!("  DASHBOARD_PORT         - Port for web server (default: 42069)");
    println!("  DASHBOARD_REFRESH_INTERVAL - Refresh interval in seconds (default: 30)");
    println!("  DASHBOARD_MAX_TRADES   - Maximum trades to display (default: 10)");
    println!();
    println!("Examples:");
    println!("  # Basic usage with default settings");
    println!("  cargo run --bin external_dashboard");
    println!();
    println!("  # Custom wallet address");
    println!("  PUMP_WALLET_ADDRESS=YourWalletAddress cargo run --bin external_dashboard");
    println!();
    println!("  # Custom port and refresh interval");
    println!("  DASHBOARD_PORT=8080 DASHBOARD_REFRESH_INTERVAL=60 cargo run --bin external_dashboard");
    println!();
}

/// Handle command line arguments
fn handle_args() -> bool {
    let args: Vec<String> = env::args().collect();
    
    for arg in &args[1..] {
        match arg.as_str() {
            "--help" | "-h" => {
                print_usage();
                return false;
            }
            "--version" | "-v" => {
                println!("External Trading Dashboard v1.0.0");
                return false;
            }
            _ => {
                println!("⚠️  Unknown argument: {}", arg);
                println!("   Use --help for usage information");
                return false;
            }
        }
    }
    
    true
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_defaults() {
        // Clear environment variables for test
        env::remove_var("PUMP_WALLET_ADDRESS");
        env::remove_var("DASHBOARD_PORT");
        
        let config = get_dashboard_config();
        
        assert_eq!(config.port, 42069);
        assert_eq!(config.refresh_interval_seconds, 30);
        assert_eq!(config.max_trades_display, 10);
    }

    #[test]
    fn test_config_from_env() {
        env::set_var("PUMP_WALLET_ADDRESS", "TestWalletAddress123");
        env::set_var("DASHBOARD_PORT", "8080");
        env::set_var("DASHBOARD_REFRESH_INTERVAL", "60");
        env::set_var("DASHBOARD_MAX_TRADES", "20");
        
        let config = get_dashboard_config();
        
        assert_eq!(config.wallet_address, "TestWalletAddress123");
        assert_eq!(config.port, 8080);
        assert_eq!(config.refresh_interval_seconds, 60);
        assert_eq!(config.max_trades_display, 20);
        
        // Clean up
        env::remove_var("PUMP_WALLET_ADDRESS");
        env::remove_var("DASHBOARD_PORT");
        env::remove_var("DASHBOARD_REFRESH_INTERVAL");
        env::remove_var("DASHBOARD_MAX_TRADES");
    }
}
