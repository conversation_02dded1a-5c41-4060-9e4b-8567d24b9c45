//! Enhanced Dashboard Binary
//!
//! Standalone executable for the enhanced trading dashboard with DexScreener integration.
//! This dashboard displays real-time market data, price changes, and trading activity
//! for tracked pump.fun tokens.

use anyhow::Result;
use blockchain_bot::enhanced_dashboard::{EnhancedDashboard, EnhancedDashboardConfig};
use std::env;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv::dotenv().ok();

    println!("🚀 Starting Enhanced Trading Dashboard with DexScreener Integration");
    println!("📊 Real-time market data, price tracking, and trading analytics");
    println!();

    // Configure dashboard
    let mut config = EnhancedDashboardConfig::default();
    
    // Override with environment variables if available
    if let Ok(port) = env::var("ENHANCED_DASHBOARD_PORT") {
        if let Ok(port_num) = port.parse::<u16>() {
            config.port = port_num;
        }
    }

    if let Ok(refresh_interval) = env::var("ENHANCED_DASHBOARD_REFRESH_SECONDS") {
        if let Ok(interval) = refresh_interval.parse::<u64>() {
            config.refresh_interval_seconds = interval;
        }
    }

    // Load tracked token addresses from environment or use defaults
    if let Ok(token_addresses_str) = env::var("ENHANCED_DASHBOARD_TRACKED_TOKENS") {
        let addresses: Vec<String> = token_addresses_str
            .split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();
        
        if !addresses.is_empty() {
            config.tracked_token_addresses = addresses;
        }
    }

    // Print configuration
    println!("⚙️  Enhanced Dashboard Configuration:");
    println!("   🌐 Port: {}", config.port);
    println!("   🔄 Refresh Interval: {}s", config.refresh_interval_seconds);
    println!("   🎯 Tracked Tokens: {}", config.tracked_token_addresses.len());
    println!("   📊 Max Display: {}", config.max_tokens_display);
    println!();

    // Print tracked tokens
    println!("🎯 Tracked Token Addresses:");
    for (i, address) in config.tracked_token_addresses.iter().enumerate() {
        println!("   {}. {}", i + 1, address);
    }
    println!();

    // Create and start dashboard
    let mut dashboard = EnhancedDashboard::new(config);
    
    match dashboard.start().await {
        Ok(()) => {
            println!("✅ Enhanced Dashboard started successfully!");
            println!("🌐 Access at: http://localhost:{}", dashboard.get_current_data().map(|d| d.config.port).unwrap_or(42070));
            println!("📊 Features:");
            println!("   • Real-time DexScreener market data");
            println!("   • 24h price changes and trading volume");
            println!("   • Market cap and liquidity tracking");
            println!("   • Buy/sell ratio analysis");
            println!("   • Direct links to DexScreener");
            println!();
            println!("🔄 Dashboard will refresh every {}s", dashboard.get_current_data().map(|d| d.config.refresh_interval_seconds).unwrap_or(30));
            println!("💡 Press Ctrl+C to stop");
            
            // Print initial status
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
            dashboard.print_status();
            
            // Keep the server running
            loop {
                tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
                dashboard.print_status();
            }
        }
        Err(e) => {
            eprintln!("❌ Failed to start Enhanced Dashboard: {}", e);
            std::process::exit(1);
        }
    }
}

/// Print usage information
fn print_usage() {
    println!("Enhanced Trading Dashboard - DexScreener Integration");
    println!();
    println!("Environment Variables:");
    println!("  ENHANCED_DASHBOARD_PORT=42070                    # Dashboard port");
    println!("  ENHANCED_DASHBOARD_REFRESH_SECONDS=30            # Refresh interval");
    println!("  ENHANCED_DASHBOARD_TRACKED_TOKENS=addr1,addr2    # Comma-separated token addresses");
    println!();
    println!("Example:");
    println!("  ENHANCED_DASHBOARD_PORT=8080 \\");
    println!("  ENHANCED_DASHBOARD_TRACKED_TOKENS=So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v \\");
    println!("  cargo run --bin enhanced_dashboard");
    println!();
    println!("Features:");
    println!("  • Real-time market data from DexScreener API");
    println!("  • 24h price changes, volume, and trading activity");
    println!("  • Market cap and liquidity tracking");
    println!("  • Buy/sell ratio analysis");
    println!("  • Responsive web interface");
    println!("  • Auto-refresh every 30 seconds");
}
