//! Enhanced Trading Dashboard with DexScreener Integration
//!
//! This module provides a web dashboard that displays real-time trading data
//! by integrating with DexScreener's API to show market data, price changes,
//! and trading activity for tracked tokens.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::net::TcpListener;
use warp::Filter;
use crate::dexscreener_client::{DexScreenerClient, EnhancedTokenData};

/// Enhanced dashboard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedDashboardConfig {
    pub port: u16,
    pub refresh_interval_seconds: u64,
    pub max_tokens_display: u32,
    pub tracked_token_addresses: Vec<String>,
}

impl Default for EnhancedDashboardConfig {
    fn default() -> Self {
        Self {
            port: 42070, // Different port from external dashboard
            refresh_interval_seconds: 30,
            max_tokens_display: 20,
            tracked_token_addresses: vec![
                // Example pump.fun token addresses - replace with actual tracked tokens
                "So11111111111111111111111111111111111111112".to_string(), // SOL
                "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
            ],
        }
    }
}

/// Enhanced dashboard data structure for API responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedDashboardData {
    pub enhanced_tokens: Vec<EnhancedTokenData>,
    pub config: EnhancedDashboardConfig,
    pub status: String,
    pub last_refresh: u64,
    pub next_refresh_in: u64,
    pub summary_stats: DashboardSummaryStats,
}

/// Summary statistics for the dashboard
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardSummaryStats {
    pub total_tokens: u32,
    pub total_market_cap: f64,
    pub total_volume_24h: f64,
    pub average_price_change_24h: f64,
    pub total_liquidity: f64,
    pub total_trades_24h: u32,
}

/// Enhanced dashboard server
pub struct EnhancedDashboard {
    config: EnhancedDashboardConfig,
    dexscreener_client: DexScreenerClient,
    data: Arc<Mutex<Option<EnhancedDashboardData>>>,
    server_handle: Option<tokio::task::JoinHandle<()>>,
}

impl EnhancedDashboard {
    /// Create a new enhanced dashboard
    pub fn new(config: EnhancedDashboardConfig) -> Self {
        Self {
            config,
            dexscreener_client: DexScreenerClient::new(),
            data: Arc::new(Mutex::new(None)),
            server_handle: None,
        }
    }

    /// Start the enhanced dashboard server
    pub async fn start(&mut self) -> Result<()> {
        println!("🚀 Starting Enhanced Dashboard on port {}", self.config.port);
        
        // Start data refresh task
        self.start_data_refresh_task().await?;
        
        // Start web server
        self.start_web_server().await?;
        
        println!("✅ Enhanced Dashboard running at http://localhost:{}", self.config.port);
        Ok(())
    }

    /// Start the data refresh background task
    async fn start_data_refresh_task(&mut self) -> Result<()> {
        let data = self.data.clone();
        let config = self.config.clone();
        let client = DexScreenerClient::new();

        let refresh_handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                tokio::time::Duration::from_secs(config.refresh_interval_seconds)
            );

            loop {
                interval.tick().await;
                
                match Self::fetch_enhanced_data(&client, &config).await {
                    Ok(dashboard_data) => {
                        let mut data_lock = data.lock().unwrap();
                        *data_lock = Some(dashboard_data);
                        println!("✅ Enhanced dashboard data refreshed");
                    }
                    Err(e) => {
                        eprintln!("❌ Failed to refresh enhanced dashboard data: {}", e);
                    }
                }
            }
        });

        // Store the handle (though we don't use it in this simple implementation)
        // self.refresh_handle = Some(refresh_handle);

        // Do initial data fetch
        let initial_data = Self::fetch_enhanced_data(&self.dexscreener_client, &self.config).await?;
        {
            let mut data_lock = self.data.lock().unwrap();
            *data_lock = Some(initial_data);
        }

        Ok(())
    }

    /// Fetch enhanced data from DexScreener
    async fn fetch_enhanced_data(
        client: &DexScreenerClient,
        config: &EnhancedDashboardConfig,
    ) -> Result<EnhancedDashboardData> {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        
        println!("🔄 Fetching enhanced token data for {} tokens", config.tracked_token_addresses.len());
        
        // Fetch enhanced token data from DexScreener
        let enhanced_tokens = client
            .get_enhanced_token_data(&config.tracked_token_addresses)
            .await?;

        // Calculate summary statistics
        let summary_stats = Self::calculate_summary_stats(&enhanced_tokens);

        let dashboard_data = EnhancedDashboardData {
            enhanced_tokens,
            config: config.clone(),
            status: "active".to_string(),
            last_refresh: now,
            next_refresh_in: config.refresh_interval_seconds,
            summary_stats,
        };

        Ok(dashboard_data)
    }

    /// Calculate summary statistics from enhanced token data
    fn calculate_summary_stats(tokens: &[EnhancedTokenData]) -> DashboardSummaryStats {
        if tokens.is_empty() {
            return DashboardSummaryStats {
                total_tokens: 0,
                total_market_cap: 0.0,
                total_volume_24h: 0.0,
                average_price_change_24h: 0.0,
                total_liquidity: 0.0,
                total_trades_24h: 0,
            };
        }

        let total_tokens = tokens.len() as u32;
        let total_market_cap = tokens.iter().map(|t| t.market_cap).sum();
        let total_volume_24h = tokens.iter().map(|t| t.volume_24h).sum();
        let total_liquidity = tokens.iter().map(|t| t.liquidity_usd).sum();
        let total_trades_24h = tokens.iter().map(|t| t.txns_24h).sum();
        
        let average_price_change_24h = tokens.iter()
            .map(|t| t.price_change_24h)
            .sum::<f64>() / tokens.len() as f64;

        DashboardSummaryStats {
            total_tokens,
            total_market_cap,
            total_volume_24h,
            average_price_change_24h,
            total_liquidity,
            total_trades_24h,
        }
    }

    /// Start the web server
    async fn start_web_server(&mut self) -> Result<()> {
        let data = self.data.clone();

        // API endpoint for enhanced dashboard data
        let api = warp::path("api")
            .and(warp::path("enhanced-dashboard"))
            .and(warp::get())
            .map(move || {
                let data_lock = data.lock().unwrap();
                match &*data_lock {
                    Some(dashboard_data) => {
                        warp::reply::json(dashboard_data)
                    }
                    None => {
                        let empty_response = serde_json::json!({
                            "status": "loading",
                            "message": "Enhanced dashboard data not yet available"
                        });
                        warp::reply::json(&empty_response)
                    }
                }
            });

        // Static HTML dashboard
        let dashboard_html = include_str!("../enhanced_dashboard.html");
        let dashboard = warp::path::end()
            .map(move || warp::reply::html(dashboard_html));

        // CORS headers for development
        let cors = warp::cors()
            .allow_any_origin()
            .allow_headers(vec!["content-type"])
            .allow_methods(vec!["GET", "POST", "DELETE"]);

        let routes = dashboard
            .or(api)
            .with(cors);

        let port = self.config.port;
        let server = warp::serve(routes)
            .run(([127, 0, 0, 1], port));

        // Spawn server in background
        let server_handle = tokio::spawn(server);
        self.server_handle = Some(server_handle);

        Ok(())
    }

    /// Update tracked token addresses
    pub fn update_tracked_tokens(&mut self, token_addresses: Vec<String>) {
        self.config.tracked_token_addresses = token_addresses;
        println!("📝 Updated tracked tokens: {} addresses", self.config.tracked_token_addresses.len());
    }

    /// Get current dashboard data
    pub fn get_current_data(&self) -> Option<EnhancedDashboardData> {
        let data_lock = self.data.lock().unwrap();
        data_lock.clone()
    }

    /// Print current status
    pub fn print_status(&self) {
        if let Some(data) = self.get_current_data() {
            println!("📊 Enhanced Dashboard Status:");
            println!("   🎯 Tracked Tokens: {}", data.summary_stats.total_tokens);
            println!("   💰 Total Market Cap: ${:.2}", data.summary_stats.total_market_cap);
            println!("   📈 24h Volume: ${:.2}", data.summary_stats.total_volume_24h);
            println!("   📊 Avg Price Change: {:.2}%", data.summary_stats.average_price_change_24h);
            println!("   💧 Total Liquidity: ${:.2}", data.summary_stats.total_liquidity);
            println!("   🔄 24h Trades: {}", data.summary_stats.total_trades_24h);
            println!("   🌐 Dashboard: http://localhost:{}", self.config.port);
        } else {
            println!("📊 Enhanced Dashboard: Loading data...");
        }
    }
}
