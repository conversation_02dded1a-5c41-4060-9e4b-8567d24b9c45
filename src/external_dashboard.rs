//! External Trading Dashboard
//!
//! This module provides a web dashboard that displays trading activity
//! by fetching data from external sources like DexScreener, completely
//! independent of the bot's internal trading logic.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::{<PERSON>, Mutex};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::net::TcpListener;
use warp::Filter;
use crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};

/// External dashboard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalDashboardConfig {
    pub wallet_address: String,
    pub port: u16,
    pub refresh_interval_seconds: u64,
    pub max_trades_display: u32,
}

impl Default for ExternalDashboardConfig {
    fn default() -> Self {
        Self {
            wallet_address: "GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus".to_string(),
            port: 42069,
            refresh_interval_seconds: 30,
            max_trades_display: 10,
        }
    }
}

/// Dashboard data structure for API responses
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExternalDashboardData {
    pub wallet_summary: WalletTradingSummary,
    pub config: ExternalDashboardConfig,
    pub status: String,
    pub last_refresh: u64,
    pub next_refresh_in: u64,
}

/// External dashboard server
pub struct ExternalDashboard {
    config: ExternalDashboardConfig,
    dexscreener_client: DexScreenerClient,
    data: Arc<Mutex<Option<ExternalDashboardData>>>,
    server_handle: Option<tokio::task::JoinHandle<()>>,
    refresh_handle: Option<tokio::task::JoinHandle<()>>,
}

impl ExternalDashboard {
    /// Create a new external dashboard
    pub fn new(config: ExternalDashboardConfig) -> Self {
        Self {
            config,
            dexscreener_client: DexScreenerClient::new(),
            data: Arc::new(Mutex::new(None)),
            server_handle: None,
            refresh_handle: None,
        }
    }

    /// Create with default configuration
    pub fn with_default_config() -> Self {
        Self::new(ExternalDashboardConfig::default())
    }

    /// Create with custom wallet address
    pub fn with_wallet(wallet_address: String) -> Self {
        let mut config = ExternalDashboardConfig::default();
        config.wallet_address = wallet_address;
        Self::new(config)
    }

    /// Start the dashboard server and data refresh loop
    pub async fn start(&mut self) -> Result<()> {
        println!("🚀 STARTING EXTERNAL TRADING DASHBOARD");
        println!("=====================================");
        println!("📊 Wallet: {}", self.config.wallet_address);
        println!("🌐 Port: {}", self.config.port);
        println!("🔄 Refresh: {}s", self.config.refresh_interval_seconds);
        println!();

        // Start data refresh loop
        self.start_data_refresh_loop().await?;

        // Start web server
        self.start_web_server().await?;

        println!("✅ External dashboard started successfully!");
        println!("🌐 Access at: http://localhost:{}", self.config.port);

        Ok(())
    }

    /// Start the data refresh loop
    async fn start_data_refresh_loop(&mut self) -> Result<()> {
        let data = self.data.clone();
        let config = self.config.clone();
        let client = DexScreenerClient::new();

        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                tokio::time::Duration::from_secs(config.refresh_interval_seconds)
            );

            loop {
                interval.tick().await;

                println!("🔄 Refreshing external trading data...");

                match client.get_wallet_summary(&config.wallet_address).await {
                    Ok(wallet_summary) => {
                        let now = SystemTime::now()
                            .duration_since(UNIX_EPOCH)
                            .unwrap()
                            .as_secs();

                        let dashboard_data = ExternalDashboardData {
                            wallet_summary,
                            config: config.clone(),
                            status: "active".to_string(),
                            last_refresh: now,
                            next_refresh_in: config.refresh_interval_seconds,
                        };

                        {
                            let mut data_lock = data.lock().unwrap();
                            *data_lock = Some(dashboard_data);
                        }

                        println!("✅ External data refreshed successfully");
                    }
                    Err(e) => {
                        println!("⚠️  Failed to refresh external data: {}", e);
                        
                        // Update status to error but keep existing data
                        if let Ok(mut data_lock) = data.lock() {
                            if let Some(ref mut existing_data) = *data_lock {
                                existing_data.status = format!("error: {}", e);
                                existing_data.last_refresh = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_secs();
                            }
                        }
                    }
                }
            }
        });

        self.refresh_handle = Some(handle);

        // Do initial data fetch
        println!("📊 Performing initial data fetch...");
        match self.dexscreener_client.get_wallet_summary(&self.config.wallet_address).await {
            Ok(wallet_summary) => {
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                let dashboard_data = ExternalDashboardData {
                    wallet_summary,
                    config: self.config.clone(),
                    status: "active".to_string(),
                    last_refresh: now,
                    next_refresh_in: self.config.refresh_interval_seconds,
                };

                {
                    let mut data_lock = self.data.lock().unwrap();
                    *data_lock = Some(dashboard_data);
                }

                println!("✅ Initial data fetch completed");
            }
            Err(e) => {
                println!("⚠️  Initial data fetch failed: {}", e);
                // Continue anyway - the refresh loop will retry
            }
        }

        Ok(())
    }

    /// Start the web server
    async fn start_web_server(&mut self) -> Result<()> {
        let data = self.data.clone();

        // API endpoint for dashboard data
        let api = warp::path("api")
            .and(warp::path("dashboard"))
            .and(warp::get())
            .map(move || {
                let data_lock = data.lock().unwrap();
                match &*data_lock {
                    Some(dashboard_data) => {
                        warp::reply::json(dashboard_data)
                    }
                    None => {
                        let empty_response = serde_json::json!({
                            "status": "loading",
                            "message": "Dashboard data not yet available"
                        });
                        warp::reply::json(&empty_response)
                    }
                }
            });

        // Static HTML dashboard
        let dashboard_html = include_str!("../external_dashboard.html");
        let dashboard = warp::path::end()
            .map(move || warp::reply::html(dashboard_html));

        // Health check endpoint
        let health = warp::path("health")
            .and(warp::get())
            .map(|| {
                warp::reply::json(&serde_json::json!({
                    "status": "healthy",
                    "timestamp": SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs()
                }))
            });

        let routes = dashboard.or(api).or(health);

        // Check if port is available
        match TcpListener::bind(format!("127.0.0.1:{}", self.config.port)).await {
            Ok(_) => {
                println!("✅ Starting web server on http://localhost:{}", self.config.port);
                
                let server = warp::serve(routes)
                    .run(([127, 0, 0, 1], self.config.port));
                
                let handle = tokio::spawn(server);
                self.server_handle = Some(handle);
                
                Ok(())
            }
            Err(e) => {
                println!("❌ Port {} is already in use: {}", self.config.port, e);
                println!("💡 Please close other applications using this port or change the port");
                Err(anyhow::anyhow!("Port {} unavailable", self.config.port))
            }
        }
    }

    /// Get current dashboard data
    pub fn get_data(&self) -> Option<ExternalDashboardData> {
        self.data.lock().unwrap().clone()
    }

    /// Stop the dashboard
    pub async fn stop(&mut self) {
        println!("🛑 Stopping external dashboard...");

        if let Some(handle) = self.refresh_handle.take() {
            handle.abort();
        }

        if let Some(handle) = self.server_handle.take() {
            handle.abort();
        }

        println!("✅ External dashboard stopped");
    }

    /// Print current status
    pub fn print_status(&self) {
        if let Some(data) = self.get_data() {
            println!("📊 EXTERNAL DASHBOARD STATUS");
            println!("============================");
            println!("Wallet: {}", data.wallet_summary.wallet_address);
            println!("Status: {}", data.status);
            println!("Total Trades: {}", data.wallet_summary.total_trades);
            println!("24h Trades: {}", data.wallet_summary.trades_24h);
            println!("24h P&L: {:.4} SOL (${:.2})", 
                data.wallet_summary.pnl_24h_sol, 
                data.wallet_summary.pnl_24h_usd);
            println!("24h Win Rate: {:.1}%", data.wallet_summary.win_rate_24h);
            println!("Recent Trades: {}", data.wallet_summary.recent_trades.len());
            println!("Last Updated: {} seconds ago", 
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs() - data.last_refresh);
        } else {
            println!("📊 External dashboard: No data available yet");
        }
    }
}
