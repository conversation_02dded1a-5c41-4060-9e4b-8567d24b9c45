//! Bonding Curve Monitoring System
//!
//! This module implements the Chainstack-inspired approach to monitor bonding curve
//! progression and identify tokens with graduation potential, replacing the risky
//! new token creation sniping strategy.

use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    commitment_config::CommitmentConfig,
    account::Account,
    program_pack::Pack,
};
use anyhow::{Result, anyhow};
use std::{
    collections::{HashMap, HashSet},
    time::{SystemTime, UNIX_EPOCH, Duration, Instant},
    str::FromStr,
};
use tokio::time::sleep;
use serde::{Deserialize, Serialize};

use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};
use crate::pump_api_client::PumpApiClient;
use crate::chainstack_client::ChainstackRpcClient;
use crate::chainstack_platform_client::ChainstackPlatformClient;
use crate::helius_client::HeliusClient;
use crate::shyft_client::ShyftClient;
// use crate::shyft_grpc_client::ShyftGrpcClient; // Disabled due to version conflicts
use crate::logs_listener::LogsListener;
use crate::logs_event_processor::TokenCreationInfo;
use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};

/// Token metrics for bonding curve analysis (from renovation guide)
#[derive(Debug, Clone)]
pub struct TokenMetrics {
    pub mint_address: Pubkey,
    pub bonding_curve: Pubkey,
    pub associated_bonding_curve: Pubkey,
    pub current_progress: f64,        // % to Raydium graduation
    pub age_hours: f64,
    pub trade_count_24h: u32,
    pub unique_holders: u32,
    pub mcap_usd: f64,
    pub sol_reserves: f64,
    pub last_updated: Instant,
    pub price_history: Vec<(Instant, f64)>, // For momentum analysis
}

/// Token scoring system (from renovation guide)
#[derive(Debug, Clone)]
pub struct TokenScore {
    pub survival_score: f32,      // Age + stability metrics
    pub momentum_score: f32,      // Trading velocity + holder growth
    pub graduation_score: f32,    // Proximity to Raydium migration
    pub risk_score: f32,         // Volatility + liquidity risks
    pub total_score: f32,        // Weighted combination
}

/// Token age data for filtering
#[derive(Debug, Clone)]
pub struct TokenAgeData {
    pub created_timestamp: u64,
}

/// Bonding curve state parsing (from pump.fun IDL)
#[derive(Debug, Clone)]
pub struct BondingCurveState {
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub token_total_supply: u64,
    pub complete: bool,  // Migration flag
    pub creator: Option<Pubkey>,
}

impl BondingCurveState {
    /// Calculate bonding curve progress percentage (from renovation guide)
    pub fn progress_percentage(&self) -> f64 {
        // Method 1: Use real_sol_reserves if available and reasonable
        let migration_threshold = 85.0; // SOL amount for graduation
        let real_sol = self.real_sol_reserves as f64 / 1e9;

        // Method 2: Fallback calculation using virtual reserves
        // Initial virtual SOL is ~30 SOL, graduation happens around 85 SOL total
        let virtual_sol = self.virtual_sol_reserves as f64 / 1e9;
        let initial_virtual_sol = 30.0;
        let progress_from_virtual = if virtual_sol > initial_virtual_sol {
            ((virtual_sol - initial_virtual_sol) / (migration_threshold - initial_virtual_sol)).min(1.0)
        } else {
            0.0
        };

        // Use real_sol_reserves if it seems reasonable, otherwise use virtual method
        let progress = if real_sol > 0.1 && real_sol < 100.0 {
            // real_sol_reserves seems valid
            (real_sol / migration_threshold).min(1.0)
        } else {
            // real_sol_reserves seems invalid, use virtual method
            progress_from_virtual
        };

        progress
    }

    /// Calculate current token price (from renovation guide)
    pub fn calculate_current_price(&self) -> f64 {
        // Use bonding curve formula: price = virtual_sol_reserves / virtual_token_reserves
        if self.virtual_token_reserves == 0 {
            return 0.0;
        }
        let virtual_sol = self.virtual_sol_reserves as f64;
        let virtual_token = self.virtual_token_reserves as f64;
        virtual_sol / virtual_token
    }

    /// Parse from account data (based on IDL structure)
    /// Handles both original (49 bytes) and extended (81+ bytes) formats
    pub fn from_account_data(data: &[u8]) -> Result<Self> {
        // Minimum size: 8 (discriminator) + 5*8 (u64 fields) + 1 (bool) = 49 bytes
        if data.len() < 49 {
            return Err(anyhow!("Invalid bonding curve account data length: {} (minimum 49)", data.len()));
        }

        println!("       🔍 Parsing {} bytes of bonding curve data", data.len());

        // Skip 8-byte discriminator, parse according to IDL
        let virtual_token_reserves = u64::from_le_bytes([
            data[8], data[9], data[10], data[11], data[12], data[13], data[14], data[15]
        ]);
        let virtual_sol_reserves = u64::from_le_bytes([
            data[16], data[17], data[18], data[19], data[20], data[21], data[22], data[23]
        ]);
        let real_token_reserves = u64::from_le_bytes([
            data[24], data[25], data[26], data[27], data[28], data[29], data[30], data[31]
        ]);
        let real_sol_reserves = u64::from_le_bytes([
            data[32], data[33], data[34], data[35], data[36], data[37], data[38], data[39]
        ]);
        let token_total_supply = u64::from_le_bytes([
            data[40], data[41], data[42], data[43], data[44], data[45], data[46], data[47]
        ]);
        let complete = data[48] != 0;

        // Creator field starts at offset 49 (32 bytes) - only in extended format
        let creator = if data.len() >= 81 {
            let creator_bytes = &data[49..81];
            Some(Pubkey::try_from(creator_bytes).unwrap_or_default())
        } else {
            None
        };

        // DEBUGGING: Calculate progress to verify parsing
        let progress_from_real_sol = (real_sol_reserves as f64 / 1e9) / 85.0;
        let progress_from_virtual_sol = ((virtual_sol_reserves as f64 - 30_000_000_000.0) / 55_000_000_000.0).max(0.0);

        println!("       📊 Parsed: vSOL={}, rSOL={}, complete={}", virtual_sol_reserves, real_sol_reserves, complete);
        println!("       🔍 Progress calc: real_sol={:.3} SOL ({:.1}%), virtual_sol_method={:.1}%",
            real_sol_reserves as f64 / 1e9, progress_from_real_sol * 100.0, progress_from_virtual_sol * 100.0);

        Ok(Self {
            virtual_token_reserves,
            virtual_sol_reserves,
            real_token_reserves,
            real_sol_reserves,
            token_total_supply,
            complete,
            creator,
        })
    }
}

/// Main bonding curve monitoring system
pub struct BondingCurveMonitor {
    rpc_client: RpcClient,
    chainstack_client: Option<ChainstackRpcClient>,
    platform_client: Option<ChainstackPlatformClient>,
    helius_client: Option<HeliusClient>,
    shyft_client: Option<ShyftClient>,
    // shyft_grpc_client: Option<ShyftGrpcClient>, // Disabled due to version conflicts
    logs_listener: Option<LogsListener>,
    aging_pipeline: Option<TokenAgingPipeline>,
    tracked_tokens: HashMap<Pubkey, TokenMetrics>,
    processed_tokens: HashSet<String>,

    // Configuration from environment
    min_age_hours: f64,
    min_trades: u32,
    min_holders: u32,
    min_progress: f64,
    sweet_spot_progress_min: f64,
    sweet_spot_progress_max: f64,
    min_mcap: f64,
    max_mcap: f64,
    min_velocity: f64,

    // Monitoring state
    last_scan_time: Instant,
    scan_interval: Duration,
}

impl BondingCurveMonitor {
    /// Create new bonding curve monitor
    pub fn new(rpc_client: RpcClient) -> Self {
        // Load configuration from environment variables
        // LEGACY VARIABLES (COMMENTED OUT - NOT USED IN SHYFT GRAPHQL FILTERING)
        let min_age_hours = 3.0; // Hardcoded - was PUMP_MIN_TOKEN_AGE_HOURS

        // LEGACY VARIABLES (COMMENTED OUT - NOT USED IN SHYFT GRAPHQL FILTERING)
        // These were used in the old API-based filtering system
        let min_trades = 50; // Hardcoded - was PUMP_MIN_TRADES_24H
        let min_holders = 10; // Hardcoded - was PUMP_MIN_HOLDERS

        // BONDING CURVE PROGRESS FILTERING REMOVED
        // All bonding curve progress filtering is now handled by Shyft GraphQL (1-80% range)
        // These legacy environment variables are disabled to prevent conflicts
        let min_progress = 0.01; // 1% - matches Shyft GraphQL minimum
        let sweet_spot_progress_min = 0.01; // 1% - matches Shyft GraphQL minimum
        let sweet_spot_progress_max = 0.80; // 80% - matches Shyft GraphQL maximum

        let min_mcap = std::env::var("PUMP_MIN_MARKET_CAP_USD")
            .unwrap_or_else(|_| "10000.0".to_string())
            .parse()
            .unwrap_or(10000.0);

        let max_mcap = std::env::var("PUMP_MAX_MARKET_CAP_USD")
            .unwrap_or_else(|_| "800000.0".to_string())
            .parse()
            .unwrap_or(800000.0);

        let min_velocity = 0.05; // Hardcoded - was PUMP_MIN_VELOCITY

        let scan_interval_seconds = std::env::var("PUMP_BONDING_CURVE_SCAN_INTERVAL")
            .unwrap_or_else(|_| "300".to_string()) // 5 minutes default
            .parse()
            .unwrap_or(300);

        // Skip Chainstack basic auth client - use standard RPC client with auth token URL
        let chainstack_client = None;
        println!("💡 Using standard RPC client with auth token URL (working method)");

        // Try to create Chainstack Platform API client if API key is available
        let platform_client = match ChainstackPlatformClient::new() {
            Ok(client) => {
                println!("✅ Chainstack Platform API client initialized");
                client.print_config();
                Some(client)
            }
            Err(e) => {
                println!("⚠️  Chainstack Platform API key not available: {}", e);
                println!("💡 Platform management features disabled");
                None
            }
        };

        // Try to create Helius client for getProgramAccounts operations
        let helius_client = match HeliusClient::new() {
            Ok(client) => {
                println!("✅ Helius client initialized for getProgramAccounts");
                client.print_config();
                Some(client)
            }
            Err(e) => {
                println!("⚠️  Helius API key not available: {}", e);
                println!("💡 Will use Shyft GraphQL or API fallback instead");
                None
            }
        };

        // Try to create Shyft GraphQL client for getProgramAccounts operations
        let shyft_client = match ShyftClient::new() {
            Ok(client) => {
                println!("✅ Shyft GraphQL client initialized for getProgramAccounts");
                client.print_config();
                Some(client)
            }
            Err(e) => {
                println!("⚠️  Shyft GraphQL client initialization failed: {}", e);
                println!("💡 Will use Helius or API fallback instead");
                None
            }
        };

        // gRPC streaming disabled due to version conflicts - will implement in future
        println!("� Shyft gRPC streaming disabled due to dependency conflicts");
        println!("   Using enhanced GraphQL with fresh data strategies instead");

        // Initialize logs listener for real-time token discovery
        let logs_listener = match (
            std::env::var("CHAINSTACK_WSS_ENDPOINT"),
            std::env::var("CHAINSTACK_NODE_AUTH_TOKEN")
        ) {
            (Ok(wss_base), Ok(auth_token)) => {
                let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
                    .expect("Valid pump.fun program ID");

                // Use authenticated WebSocket URL (working endpoint from test)
                let wss_url = format!("{}/{}", wss_base, auth_token);
                let listener = LogsListener::new(wss_url.clone(), pump_program);
                println!("✅ Logs listener initialized for real-time token discovery");
                println!("🔧 Logs Listener Configuration:");
                println!("   WebSocket URL: {} (with auth token)", wss_base);
                println!("   Program: {} (pump.fun)", pump_program);
                println!("   Method: logsSubscribe for real-time token creations");
                println!("   Status: TESTED AND WORKING ✅");
                Some(listener)
            }
            _ => {
                // Fallback to public Solana endpoint (also tested and working)
                let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
                    .expect("Valid pump.fun program ID");

                let wss_url = "wss://api.mainnet-beta.solana.com".to_string();
                let listener = LogsListener::new(wss_url.clone(), pump_program);
                println!("✅ Logs listener initialized with public Solana endpoint");
                println!("🔧 Logs Listener Configuration:");
                println!("   WebSocket URL: {} (public fallback)", wss_url);
                println!("   Program: {} (pump.fun)", pump_program);
                println!("   Method: logsSubscribe for real-time token creations");
                println!("   Status: TESTED AND WORKING ✅");
                Some(listener)
            }
        };

        // Initialize token aging pipeline for graduation candidate tracking
        let aging_pipeline = if logs_listener.is_some() {
            // Create a new RPC client for the aging pipeline
            let pipeline_rpc_client = solana_client::rpc_client::RpcClient::new_with_commitment(
                rpc_client.url().to_string(),
                rpc_client.commitment()
            );
            let pipeline = TokenAgingPipeline::new(
                pipeline_rpc_client,
                120, // min_age_seconds: 2 minutes for tokens to establish
                1800, // max_age_seconds: 30 minutes maximum tracking
                0.20, // min_progress: 20% bonding curve progress
                0.60, // max_progress: 60% bonding curve progress
            );
            println!("✅ Token aging pipeline initialized");
            println!("🔧 Aging Pipeline Configuration:");
            println!("   Min age: 120s (2 minutes for establishment)");
            println!("   Max age: 1800s (1 hour maximum tracking)");
            println!("   Progress range: 20%-60% (graduation potential)");
            println!("   Purpose: Convert fresh logsSubscribe tokens into graduation candidates");
            Some(pipeline)
        } else {
            println!("⚠️  Aging pipeline disabled (no logs listener)");
            None
        };

        Self {
            rpc_client,
            chainstack_client,
            platform_client,
            helius_client,
            shyft_client,
            // shyft_grpc_client, // Disabled
            logs_listener,
            aging_pipeline,
            tracked_tokens: HashMap::new(),
            processed_tokens: HashSet::new(),
            min_age_hours,
            min_trades,
            min_holders,
            min_progress,
            sweet_spot_progress_min,
            sweet_spot_progress_max,
            min_mcap,
            max_mcap,
            min_velocity,
            last_scan_time: Instant::now() - Duration::from_secs(scan_interval_seconds + 1), // Force first scan
            scan_interval: Duration::from_secs(scan_interval_seconds),
        }
    }

    /// Print configuration for debugging
    pub fn print_configuration(&self) {
        println!("🔧 BONDING CURVE MONITOR CONFIGURATION:");
        println!("   📅 Min Token Age: {:.1} hours", self.min_age_hours);
        println!("   📊 Min Trades (24h): {}", self.min_trades);
        println!("   👥 Min Holders: {}", self.min_holders);
        println!("   📈 Min Progress: {:.1}%", self.min_progress * 100.0);
        println!("   🎯 Sweet Spot: {:.1}%-{:.1}%", 
            self.sweet_spot_progress_min * 100.0, 
            self.sweet_spot_progress_max * 100.0);
        println!("   💰 Market Cap Range: ${:.0}-${:.0}", self.min_mcap, self.max_mcap);
        println!("   ⚡ Min Velocity: {:.2}%/hour", self.min_velocity * 100.0);
        println!("   🔄 Scan Interval: {} seconds", self.scan_interval.as_secs());
    }

    /// Check if it's time to scan for new opportunities
    pub fn should_scan(&self) -> bool {
        self.last_scan_time.elapsed() >= self.scan_interval
    }

    /// Test Platform API connection if available
    pub async fn test_platform_api(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if let Some(ref platform_client) = self.platform_client {
            println!("🔍 Testing Chainstack Platform API...");

            // Test basic connection
            if let Err(e) = platform_client.test_connection().await {
                println!("❌ Platform API test failed: {}", e);
                return Err(e.into());
            }

            // Get comprehensive node information
            if let Err(e) = platform_client.get_comprehensive_node_info().await {
                println!("⚠️  Could not get node info: {}", e);
            }

            println!();
        } else {
            println!("⚠️  Platform API client not available - skipping test");
        }

        Ok(())
    }

    /// Scan for tokens with graduation potential (main entry point)
    pub async fn scan_for_opportunities(&mut self) -> Result<Vec<LiveTokenData>> {
        if !self.should_scan() {
            return Ok(vec![]);
        }

        println!("🔍 BONDING CURVE SCAN: Looking for graduation candidates...");
        self.last_scan_time = Instant::now();

        let mut opportunities = Vec::new();

        // Phase 0: Check aging pipeline for matured graduation candidates (PRIORITY)
        if let Some(ref mut aging_pipeline) = self.aging_pipeline {
            match aging_pipeline.check_for_graduation_candidates().await {
                Ok(graduation_candidates) => {
                    if !graduation_candidates.is_empty() {
                        println!("🎓 AGING PIPELINE PRODUCED {} GRADUATION CANDIDATES!", graduation_candidates.len());

                        for candidate in graduation_candidates {
                            println!("🚀 PROCESSING AGED GRADUATION CANDIDATE: {} (age: {}s, progress: {:.1}%)",
                                candidate.token_info.symbol, candidate.age_seconds, candidate.current_progress * 100.0);

                            // Show price analysis for dip-buy functionality
                            if let Some(threshold_price) = candidate.graduation_threshold_price {
                                let price_change = ((candidate.current_price - threshold_price) / threshold_price) * 100.0;
                                println!("     💰 PRICE ANALYSIS:");
                                println!("       🎯 THRESHOLD PRICE: {:.9} SOL/token (when first noticed above threshold)", threshold_price);
                                println!("       💰 CURRENT PRICE: {:.9} SOL/token ({:+.1}% change)", candidate.current_price, price_change);

                                if price_change < 0.0 {
                                    println!("       📉 DIP OPPORTUNITY: {:.1}% price drop - PERFECT FOR DIP-BUY!", price_change.abs());
                                } else if price_change > 0.0 {
                                    println!("       📈 PRICE APPRECIATION: {:.1}% increase since threshold", price_change);
                                } else {
                                    println!("       ➡️  STABLE PRICE: No significant change since threshold");
                                }
                            } else {
                                println!("     💰 CURRENT PRICE: {:.9} SOL/token (no threshold price tracked)", candidate.current_price);
                            }

                            // Convert graduation candidate to LiveTokenData for buy logic
                            match self.analyze_token_bonding_curve_with_address(&candidate.mint, &candidate.bonding_curve).await {
                                Ok(Some(live_token)) => {
                                    println!("   ✅ Graduation candidate analysis successful: {}", live_token.name);

                                    // Apply scoring system
                                    if let Some(score) = self.calculate_buy_signal(&live_token) {
                                        println!("   📊 Graduation candidate score: {:.2} (threshold: 0.3)", score.total_score);
                                        if score.total_score > 0.3 { // Lower threshold for aged candidates
                                            println!("🎯 AGED GRADUATION CANDIDATE APPROVED: {} (score: {:.2})",
                                                live_token.name, score.total_score);
                                            opportunities.push(live_token);
                                        } else {
                                            println!("   ❌ Score too low for aged candidate: {:.2} < 0.3", score.total_score);
                                        }
                                    } else {
                                        println!("   ❌ Could not calculate buy signal for aged candidate");
                                    }
                                }
                                Ok(None) => {
                                    println!("   ❌ Aged candidate analysis returned None (filtered out)");
                                }
                                Err(e) => {
                                    println!("   ❌ Aged candidate analysis failed: {}", e);
                                }
                            }
                        }

                        // If we found aged candidates, return them immediately (highest priority)
                        if !opportunities.is_empty() {
                            println!("🎉 RETURNING {} AGED GRADUATION CANDIDATES FOR IMMEDIATE TRADING", opportunities.len());
                            return Ok(opportunities);
                        }
                    }
                }
                Err(e) => {
                    println!("   ⚠️  Aging pipeline check failed: {}", e);
                }
            }
        }

        // Phase 1: Discover tokens via API (existing tokens with activity)
        match self.discover_active_tokens().await {
            Ok(discovered) => {
                println!("   📊 Discovered {} active tokens", discovered.len());

                // Phase 2: Analyze each token's bonding curve
                for (token_mint, bonding_curve) in discovered {
                    println!("   🔍 Analyzing token: {} (curve: {})", token_mint, bonding_curve);
                    match self.analyze_token_bonding_curve_with_address(&token_mint, &bonding_curve).await {
                        Ok(Some(live_token)) => {
                            println!("   ✅ Token analysis successful: {}", live_token.name);
                            // Phase 3: Apply scoring system
                            if let Some(score) = self.calculate_buy_signal(&live_token) {
                                println!("   📊 Token score: {:.2} (threshold: 0.4)", score.total_score);
                                if score.total_score > 0.35 { // Adjusted threshold for graduation candidates
                                    println!("🎯 GRADUATION CANDIDATE OPPORTUNITY: {} (score: {:.2})",
                                        live_token.name, score.total_score);
                                    opportunities.push(live_token);
                                } else {
                                    println!("   ❌ Score too low: {:.2} < 0.4", score.total_score);
                                }
                            } else {
                                println!("   ❌ Could not calculate buy signal");
                            }
                        }
                        Ok(None) => {
                            println!("   ❌ Token analysis returned None (filtered out)");
                        }
                        Err(e) => {
                            println!("   ❌ Token analysis failed: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                println!("⚠️  Token discovery failed: {}", e);
            }
        }

        println!("✅ Bonding curve scan complete: {} opportunities found", opportunities.len());
        Ok(opportunities)
    }

    /// Get the current size of the aging pipeline for rate limiting decisions
    pub fn get_aging_pipeline_size(&self) -> usize {
        if let Some(ref aging_pipeline) = self.aging_pipeline {
            aging_pipeline.tracked_tokens.len()
        } else {
            0
        }
    }

    /// Start continuous aging pipeline background task
    /// This runs independently and continuously processes aging tokens
    pub async fn start_continuous_aging_pipeline(&mut self) -> Result<()> {
        if let Some(ref mut aging_pipeline) = self.aging_pipeline {
            println!("🔄 STARTING CONTINUOUS AGING PIPELINE");
            println!("   ⏰ Check interval: 5 seconds (optimized for 2-min scans)");
            println!("   🎯 Will continuously monitor {} tracked tokens", aging_pipeline.tracked_tokens.len());

            // Clone the pipeline for the background task
            let mut pipeline_clone = aging_pipeline.clone();

            // Spawn background task
            tokio::spawn(async move {
                let mut check_interval = tokio::time::interval(tokio::time::Duration::from_secs(5)); // Optimized for 2-min scans
                let mut check_count = 0;

                loop {
                    check_interval.tick().await;
                    check_count += 1;

                    if !pipeline_clone.tracked_tokens.is_empty() {
                        println!("🔄 AGING PIPELINE CHECK #{} - {} tokens tracked",
                            check_count, pipeline_clone.tracked_tokens.len());

                        match pipeline_clone.check_for_graduation_candidates().await {
                            Ok(candidates) => {
                                if !candidates.is_empty() {
                                    println!("🎓 CONTINUOUS PIPELINE: {} NEW GRADUATION CANDIDATES READY!", candidates.len());
                                    for candidate in &candidates {
                                        println!("   🚀 READY: {} (age: {}s, progress: {:.1}%)",
                                            candidate.token_info.symbol,
                                            candidate.age_seconds,
                                            candidate.current_progress * 100.0);
                                    }
                                    println!("   💡 These will be picked up in the next scan cycle for trading");
                                } else {
                                    println!("   ⏳ No new graduation candidates yet - tokens still aging...");
                                }
                            }
                            Err(e) => {
                                println!("   ❌ Continuous pipeline check failed: {}", e);
                            }
                        }
                    } else {
                        if check_count % 10 == 0 { // Log every 5 minutes when empty
                            println!("   📭 Aging pipeline empty - waiting for fresh tokens from logsSubscribe");
                        }
                    }
                }
            });

            println!("✅ Continuous aging pipeline started successfully");
            Ok(())
        } else {
            println!("⚠️  No aging pipeline available to start");
            Ok(())
        }
    }

    /// Discover active tokens using OPTIMIZED logsSubscribe approach
    /// Focus on the working method: logsSubscribe for real-time token discovery
    async fn discover_active_tokens(&mut self) -> Result<Vec<(Pubkey, Pubkey)>> {
        println!("   🔍 Using OPTIMIZED token discovery (logsSubscribe only)");

        // Method 1: Real-time logsSubscribe for fresh token creations (PRIMARY AND ONLY)
        if self.logs_listener.is_some() {
            match self.discover_tokens_via_logs().await {
                Ok(discovered_tokens) if !discovered_tokens.is_empty() => {
                    println!("   🌊 logsSubscribe found {} FRESH tokens", discovered_tokens.len());
                    return Ok(discovered_tokens);
                }
                Ok(_) => {
                    println!("   📭 logsSubscribe returned no new tokens this cycle");
                }
                Err(e) => {
                    println!("   ❌ logsSubscribe failed: {}", e);
                }
            }
        } else {
            println!("   ⚠️  logsSubscribe not available");
        }

        // No fallbacks - focus on speed and efficiency
        // logsSubscribe is the most reliable and fastest method
        println!("   📊 Discovered 0 active tokens");
        Ok(vec![])
    }

    /// Enhanced logsSubscribe Token Discovery with Aging Pipeline
    /// This method uses a two-phase approach:
    /// 1. Capture fresh tokens via logsSubscribe and add to aging pipeline
    /// 2. Check aging pipeline for tokens that have matured into graduation candidates
    async fn discover_tokens_via_logs(&mut self) -> Result<Vec<(Pubkey, Pubkey)>> {
        println!("   🌊 Starting ENHANCED logsSubscribe with aging pipeline...");

        let mut discovered_tokens = Vec::new();

        // Phase 1: Check aging pipeline for matured graduation candidates
        if let Some(ref mut aging_pipeline) = self.aging_pipeline {
            match aging_pipeline.check_for_graduation_candidates().await {
                Ok(graduation_candidates) => {
                    if !graduation_candidates.is_empty() {
                        println!("   🎓 Aging pipeline produced {} graduation candidates!", graduation_candidates.len());

                        for candidate in graduation_candidates {
                            discovered_tokens.push((candidate.mint, candidate.bonding_curve));
                            println!("   ✅ AGED graduation candidate: {} (age: {}s, progress: {:.1}%)",
                                candidate.token_info.symbol, candidate.age_seconds, candidate.current_progress * 100.0);
                        }

                        // Return aged candidates immediately - they're higher quality
                        if !discovered_tokens.is_empty() {
                            println!("   🎯 Returning {} aged graduation candidates from pipeline", discovered_tokens.len());
                            return Ok(discovered_tokens);
                        }
                    }
                }
                Err(e) => {
                    println!("   ⚠️  Aging pipeline check failed: {}", e);
                }
            }
        }

        // Phase 2: Listen for fresh tokens and add to aging pipeline
        if let Some(ref logs_listener) = self.logs_listener {
            match logs_listener.listen_for_tokens(Some(8)).await {
            Ok(token_creations) => {
                if !token_creations.is_empty() {
                    println!("   🌊 Discovered {} fresh tokens from logsSubscribe", token_creations.len());

                    // Add fresh tokens to aging pipeline for future graduation
                    if let Some(ref mut aging_pipeline) = self.aging_pipeline {
                        aging_pipeline.add_fresh_tokens(token_creations.clone());
                        println!("   📝 Added {} fresh tokens to aging pipeline", token_creations.len());
                    }

                    // Also check if any fresh tokens are immediate graduation candidates (rare but possible)
                    let mut immediate_candidates = 0;
                    for token_creation in &token_creations {
                        let mint_pubkey = token_creation.mint;
                        let bonding_curve_pubkey = token_creation.bonding_curve;

                        // Quick check for immediate graduation potential (very rare for fresh tokens)
                        match self.rpc_client.get_account(&bonding_curve_pubkey) {
                            Ok(account) => {
                                if let Ok(curve_data) = BondingCurveState::from_account_data(&account.data) {
                                    if self.is_valid_graduation_candidate(&curve_data, &mint_pubkey) {
                                        discovered_tokens.push((mint_pubkey, bonding_curve_pubkey));
                                        immediate_candidates += 1;
                                        println!("   🚀 IMMEDIATE graduation candidate: {} (fresh but qualified!)",
                                            token_creation.symbol);
                                    }
                                }
                            }
                            Err(_) => {} // Ignore RPC errors for fresh tokens
                        }
                    }

                    if immediate_candidates > 0 {
                        println!("   ⚡ Found {} immediate graduation candidates from fresh tokens", immediate_candidates);
                    } else {
                        println!("   ⏳ No immediate candidates - tokens added to aging pipeline for future graduation");
                    }
                }

                Ok(discovered_tokens)
            }
            Err(e) => {
                println!("   ❌ logsSubscribe failed: {}", e);
                Err(e)
            }
        }
        } else {
            println!("   ⚠️  No logs listener available");
            Ok(vec![])
        }
    }

    /// Check if a token is a valid graduation candidate
    fn is_valid_graduation_candidate(&self, curve_data: &BondingCurveState, _mint: &Pubkey) -> bool {
        // Check if token is not already graduated
        if curve_data.complete {
            return false;
        }

        // Check progress range (20-60% for graduation potential)
        let progress = curve_data.progress_percentage();
        if progress < 0.20 || progress > 0.60 {
            return false;
        }

        // Check minimum SOL reserves
        let sol_reserves = curve_data.real_sol_reserves as f64 / 1e9;
        if sol_reserves < 20.0 {
            return false;
        }

        true
    }

    // gRPC streaming method disabled due to dependency conflicts
    // Will be implemented in future when Solana version compatibility is resolved

    // Shyft GraphQL methods removed for performance optimization
    // These methods were causing delays and validation failures

    // Shyft GraphQL direct method removed for performance optimization

    // Shyft GraphQL specialized method removed for performance optimization

    /// Limited getProgramAccounts method with strict filters to avoid rate limiting
    /// Based on learning-examples/bonding-curve-progress/get_graduating_tokens.py
    async fn discover_tokens_via_program_accounts_limited(&self) -> Result<Vec<Pubkey>> {
        let pump_program_id = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?;

        println!("   🔍 Scanning pump.fun program accounts for bonding curves...");

        // Get filtered bonding curve accounts from the pump.fun program
        // Use filters to reduce RPC load and avoid rate limiting
        use solana_client::rpc_filter::{RpcFilterType, Memcmp};
        use solana_client::rpc_config::{RpcProgramAccountsConfig, RpcAccountInfoConfig};
        use solana_account_decoder::UiAccountEncoding;

        // Try Chainstack getProgramAccounts with STRICT filters to avoid 403
        println!("   🔍 Using STRICT filters to avoid 403 Forbidden...");

        // Create STRICT filters for 15-50% progress, non-completed bonding curves
        let filters = vec![
            // Filter 1: Account data size (bonding curves are exactly 81 bytes)
            RpcFilterType::DataSize(81),

            // Filter 2: Non-completed bonding curves (complete flag = false at offset 48)
            RpcFilterType::Memcmp(Memcmp::new(
                48, // Offset for 'complete' boolean field
                solana_client::rpc_filter::MemcmpEncodedBytes::Bytes(vec![0]), // false = 0
            )),

            // Filter 3: Minimum SOL reserves for 15% progress (~12.75 SOL = 12,750,000,000 lamports)
            // At offset 32-39 (real_sol_reserves as u64 little-endian)
            RpcFilterType::Memcmp(Memcmp::new(
                32, // Offset for real_sol_reserves
                solana_client::rpc_filter::MemcmpEncodedBytes::Base58("3gF2uAkN".to_string()), // ~12+ SOL minimum
            )),
        ];

        let config = RpcProgramAccountsConfig {
            filters: Some(filters),
            account_config: RpcAccountInfoConfig {
                encoding: Some(UiAccountEncoding::Base64),
                commitment: Some(self.rpc_client.commitment()),
                data_slice: None,
                min_context_slot: None,
            },
            with_context: Some(false),
        };

        println!("   🌐 Using filtered getProgramAccounts with auth token...");
        let result = self.rpc_client.get_program_accounts_with_config(&pump_program_id, config);

        match result {
            Ok(accounts) => {
                let mut discovered_tokens = Vec::new();
                let mut processed_count = 0;
                let mut valid_curves = 0;

                println!("   📊 Processing {} filtered bonding curve accounts...", accounts.len());

                for (pubkey, account) in accounts.iter().take(20) { // Process max 20 filtered results
                    processed_count += 1;

                    // Parse bonding curve data to verify progress range (20-60%)
                    if account.data.len() >= 81 {
                        if let Ok(curve_state) = BondingCurveState::from_account_data(&account.data) {
                            let progress = curve_state.progress_percentage();

                            // Apply 15-50% progress filter
                            if progress >= 0.2 && progress <= 0.6 && !curve_state.complete {
                                if let Some(mint) = self.derive_mint_from_bonding_curve(pubkey) {
                                    discovered_tokens.push(mint);
                                    valid_curves += 1;

                                    println!("   🎯 Found candidate: progress {:.1}%", progress * 100.0);

                                    if valid_curves >= 5 { // LIMIT TO 5 RESULTS
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if processed_count % 20 == 0 {
                        println!("   ⏳ Processed {} accounts, found {} candidates...", processed_count, valid_curves);
                    }
                }

                println!("   ✅ Chainstack scan complete: {} accounts processed, {} candidates found",
                    processed_count, valid_curves);

                Ok(discovered_tokens)
            }
            Err(e) => {
                Err(anyhow!("Chainstack getProgramAccounts failed: {}", e))
            }
        }
    }

    // Helius getProgramAccounts method removed for performance optimization
    // This method was causing rate limiting errors

    // pump.fun API fallback method removed for performance optimization
    // This method was causing 503 Service Unavailable errors

    /// Legacy fallback method: Use a curated list of known active tokens
    /// This avoids RPC rate limiting while still providing tokens to analyze
    async fn discover_tokens_via_fallback_method(&self) -> Result<Vec<Pubkey>> {
        println!("   🎯 Using legacy fallback method with curated token list...");

        // In a production system, this would be populated from:
        // 1. Previous successful scans (cached results)
        // 2. External token lists or APIs
        // 3. Community-curated lists of active pump.fun tokens

        // For now, return empty list to avoid errors
        // This ensures the bot continues running without crashing
        println!("   📝 Legacy fallback method: No curated tokens available");
        println!("   💡 Consider implementing token caching or external token sources");

        Ok(vec![])
    }

    /// Fetch real account data from RPC and extract mint
    /// This gets the actual bonding curve account data instead of using mock data
    async fn fetch_and_extract_mint_from_rpc(&self, bonding_curve_pubkey: &Pubkey) -> Option<Pubkey> {
        // Try to get account data from RPC
        match self.rpc_client.get_account(bonding_curve_pubkey) {
            Ok(account) => {
                println!("     📡 Fetched real account data: {} bytes", account.data.len());
                self.extract_mint_from_bonding_curve_data(&account.data)
            }
            Err(e) => {
                println!("     ❌ Failed to fetch account data from RPC: {}", e);
                None
            }
        }
    }

    /// Get mint address from bonding curve address using RPC-based PDA derivation
    /// This is more reliable than external APIs since it uses the blockchain directly
    async fn get_mint_from_bonding_curve_api(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {
        println!("       🔍 Attempting to derive mint from bonding curve: {}", bonding_curve);

        // METHOD 1: Try PDA reverse-engineering with common mint patterns
        // This is computationally expensive but works offline
        if let Some(mint) = self.derive_mint_via_pda_search(bonding_curve).await {
            println!("       ✅ Found mint via PDA search: {}", mint);
            return Some(mint);
        }

        // METHOD 2: Fallback to pump.fun API (when available)
        match reqwest::get("https://frontend-api.pump.fun/coins?offset=0&limit=1000&sort=created_timestamp&order=DESC").await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        if let Some(tokens) = json.as_array() {
                            for token in tokens {
                                if let Some(mint_str) = token.get("mint").and_then(|v| v.as_str()) {
                                    if let Ok(mint) = Pubkey::from_str(mint_str) {
                                        // Calculate the expected bonding curve address for this mint
                                        let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
                                        let (expected_curve, _) = Pubkey::find_program_address(
                                            &[b"bonding-curve", mint.as_ref()],
                                            &pump_program
                                        );

                                        if expected_curve == *bonding_curve {
                                            println!("       ✅ Found matching mint via API: {}", mint);
                                            return Some(mint);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Err(e) => {
                println!("       ❌ Failed to query pump.fun API: {}", e);
            }
        }

        println!("       ❌ No matching mint found for bonding curve");
        None
    }

    /// Validate bonding curve freshness via RPC to detect stale Shyft data
    /// Returns (mint, is_fresh, progress, complete) if successful
    async fn validate_bonding_curve_freshness(&self, bonding_curve: &Pubkey) -> Result<Option<(Pubkey, bool, f64, bool)>> {
        // Step 1: Get real-time bonding curve account data via RPC
        match self.rpc_client.get_account(bonding_curve) {
            Ok(account) => {
                // Step 2: Parse the real bonding curve state
                if let Ok(curve_state) = BondingCurveState::from_account_data(&account.data) {
                    let progress = curve_state.progress_percentage();
                    let complete = curve_state.complete;

                    // Step 3: Try to derive the mint from the bonding curve
                    if let Some(mint) = self.derive_mint_via_pda_search(bonding_curve).await {
                        // Data is fresh if we can parse it and derive the mint
                        Ok(Some((mint, true, progress, complete)))
                    } else {
                        // Try alternative mint derivation via pump.fun API
                        if let Some(mint) = self.get_mint_from_bonding_curve_api(bonding_curve).await {
                            Ok(Some((mint, true, progress, complete)))
                        } else {
                            println!("       ❌ Could not derive mint for validation");
                            Ok(None)
                        }
                    }
                } else {
                    println!("       ❌ Could not parse bonding curve account data");
                    Ok(None)
                }
            }
            Err(e) => {
                println!("       ❌ RPC validation failed: {}", e);
                // If RPC fails, we can't validate - treat as stale
                Ok(None)
            }
        }
    }

    /// Derive mint from bonding curve using efficient PDA reverse-engineering
    /// This method uses mathematical approach to avoid heavy RPC calls
    async fn derive_mint_via_pda_search(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {
        println!("       🔍 Starting efficient PDA reverse-engineering for bonding curve: {}", bonding_curve);

        // APPROACH: Since RPC calls are rate limited, use pump.fun API to get recent mints
        // and check which one produces the given bonding curve address

        // METHOD 1: Try pump.fun bonding curve lookup API (direct approach)
        if let Some(mint) = self.try_pump_fun_bonding_curve_lookup(bonding_curve).await {
            println!("       ✅ Found mint via pump.fun bonding curve lookup: {}", mint);
            return Some(mint);
        }

        // METHOD 2: Try pump.fun API for recent tokens (most likely to match)
        match reqwest::get("https://frontend-api.pump.fun/coins?offset=0&limit=200&sort=created_timestamp&order=DESC").await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        if let Some(tokens) = json.as_array() {
                            println!("       📊 Checking {} recent tokens from pump.fun API", tokens.len());

                            for token in tokens {
                                if let Some(mint_str) = token.get("mint").and_then(|v| v.as_str()) {
                                    if let Ok(mint) = Pubkey::from_str(mint_str) {
                                        // Calculate the expected bonding curve address for this mint
                                        let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
                                        let (expected_curve, _) = Pubkey::find_program_address(
                                            &[b"bonding-curve", mint.as_ref()],
                                            &pump_program
                                        );

                                        if expected_curve == *bonding_curve {
                                            println!("       ✅ Found matching mint via API reverse-engineering: {}", mint);
                                            return Some(mint);
                                        }
                                    }
                                }
                            }

                            println!("       ❌ No matching mint found in {} recent tokens", tokens.len());
                        }
                    }
                }
            }
            Err(e) => {
                println!("       ❌ pump.fun API failed for mint derivation: {}", e);
            }
        }

        // METHOD 2: Try Helius RPC for token account queries (different rate limits)
        if let Some(ref helius_client) = self.helius_client {
            println!("       🔄 Trying Helius RPC for token account queries...");
            match helius_client.rpc_client().get_token_accounts_by_owner(
                bonding_curve,
                solana_client::rpc_request::TokenAccountsFilter::ProgramId(
                    spl_token::id()
                ),
            ) {
                Ok(token_accounts) => {
                    println!("       📊 Helius found {} token accounts for bonding curve", token_accounts.len());

                    for account in token_accounts {
                        // Convert UiAccountData to bytes
                        if let Some(data_bytes) = account.account.data.decode() {
                            if let Ok(token_account) = spl_token::state::Account::unpack(&data_bytes) {
                                let mint = token_account.mint;

                                // Verify this mint produces the correct bonding curve PDA
                                let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
                                let (expected_curve, _) = Pubkey::find_program_address(
                                    &[b"bonding-curve", mint.as_ref()],
                                    &pump_program
                                );

                                if expected_curve == *bonding_curve {
                                    println!("       ✅ Found matching mint via Helius token account: {}", mint);
                                    return Some(mint);
                                }
                            }
                        }
                    }

                    println!("       ❌ No valid mint found in Helius token accounts");
                }
                Err(e) => {
                    println!("       ❌ Helius token account query failed: {}", e);
                }
            }
        }

        // METHOD 3: Fallback - try Chainstack RPC if Helius fails
        match self.rpc_client.get_token_accounts_by_owner(
            bonding_curve,
            solana_client::rpc_request::TokenAccountsFilter::ProgramId(
                spl_token::id()
            ),
        ) {
            Ok(token_accounts) => {
                println!("       📊 Found {} token accounts for bonding curve", token_accounts.len());

                for account in token_accounts {
                    // Convert UiAccountData to bytes
                    if let Some(data_bytes) = account.account.data.decode() {
                        if let Ok(token_account) = spl_token::state::Account::unpack(&data_bytes) {
                            let mint = token_account.mint;

                            // Verify this mint produces the correct bonding curve PDA
                            let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
                            let (expected_curve, _) = Pubkey::find_program_address(
                                &[b"bonding-curve", mint.as_ref()],
                                &pump_program
                            );

                            if expected_curve == *bonding_curve {
                                println!("       ✅ Found matching mint via token account: {}", mint);
                                return Some(mint);
                            }
                        }
                    }
                }

                println!("       ❌ No valid mint found in token accounts");
            }
            Err(e) => {
                println!("       ❌ Failed to query token accounts (rate limited): {}", e);
            }
        }

        // No more fallbacks - focus on speed and efficiency
        // Mathematical PDA brute force removed for performance optimization
        println!("       ❌ No matching mint found for bonding curve");
        None
    }

    /// Extract mint address from bonding curve account data (legacy method)
    /// NOTE: The mint is NOT stored in bonding curve account data!
    fn extract_mint_from_bonding_curve_data(&self, account_data: &[u8]) -> Option<Pubkey> {
        println!("       ❌ CRITICAL: Mint is NOT stored in bonding curve account data!");
        println!("       💡 The bonding curve address is derived FROM the mint using PDA");
        println!("       💡 Account data length: {} bytes", account_data.len());
        None
    }

    /// Try pump.fun bonding curve lookup API to get mint directly
    /// This uses pump.fun's internal APIs that might provide direct mint information
    async fn try_pump_fun_bonding_curve_lookup(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {
        println!("       🔍 Trying pump.fun bonding curve lookup API...");

        // METHOD 1: Try pump.fun's coin detail API with bonding curve address
        let bonding_curve_str = bonding_curve.to_string();
        let detail_url = format!("https://frontend-api.pump.fun/coins/{}", bonding_curve_str);

        match reqwest::get(&detail_url).await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        if let Some(mint_str) = json.get("mint").and_then(|v| v.as_str()) {
                            if let Ok(mint) = Pubkey::from_str(mint_str) {
                                println!("       ✅ Found mint via pump.fun detail API: {}", mint);
                                return Some(mint);
                            }
                        }

                        // Also try "address" field
                        if let Some(mint_str) = json.get("address").and_then(|v| v.as_str()) {
                            if let Ok(mint) = Pubkey::from_str(mint_str) {
                                println!("       ✅ Found mint via pump.fun detail API (address field): {}", mint);
                                return Some(mint);
                            }
                        }
                    }
                }
            }
            Err(e) => {
                println!("       ❌ pump.fun detail API failed: {}", e);
            }
        }

        // METHOD 2: Try pump.fun's search API
        let search_url = format!("https://frontend-api.pump.fun/search/coins?q={}", bonding_curve_str);

        match reqwest::get(&search_url).await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        if let Some(results) = json.as_array() {
                            for result in results {
                                if let Some(mint_str) = result.get("mint").and_then(|v| v.as_str()) {
                                    if let Ok(mint) = Pubkey::from_str(mint_str) {
                                        // Verify this mint produces the correct bonding curve
                                        let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
                                        let (expected_curve, _) = Pubkey::find_program_address(
                                            &[b"bonding-curve", mint.as_ref()],
                                            &pump_program
                                        );

                                        if expected_curve == *bonding_curve {
                                            println!("       ✅ Found mint via pump.fun search API: {}", mint);
                                            return Some(mint);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Err(e) => {
                println!("       ❌ pump.fun search API failed: {}", e);
            }
        }

        println!("       ❌ pump.fun bonding curve lookup failed");
        None
    }

    // Mathematical PDA brute force method removed for performance optimization
    // This method was causing delays and never succeeding in practice

    /// Helper method: Derive mint address from bonding curve address (fallback)
    /// This uses the pump.fun IDL structure to find the associated mint
    fn derive_mint_from_bonding_curve(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {
        // In pump.fun, the bonding curve address is derived from the mint
        // We need to reverse-engineer this or use a different approach

        // For now, we'll use the bonding curve address as a proxy
        // In a full implementation, you would:
        // 1. Parse the bonding curve account data to get the mint field
        // 2. Or use the pump.fun IDL to derive the mint from the curve address

        // Placeholder: return the bonding curve address itself
        // This will be refined when we have full IDL parsing
        Some(*bonding_curve)
    }

    /// Get token metadata for age checking (simplified structure)
    async fn get_token_metadata_for_age_check(&self, mint: &Pubkey) -> Result<TokenAgeData> {
        // Use pump.fun API to get token creation timestamp
        let api_url = format!("https://frontend-api.pump.fun/coins/{}", mint);

        match reqwest::get(&api_url).await {
            Ok(response) => {
                if response.status().is_success() {
                    if let Ok(json) = response.json::<serde_json::Value>().await {
                        // Extract creation timestamp from pump.fun API response
                        if let Some(created_timestamp) = json.get("created_timestamp").and_then(|v| v.as_u64()) {
                            return Ok(TokenAgeData {
                                created_timestamp,
                            });
                        }
                    }
                }
            }
            Err(e) => {
                println!("   ⚠️  Failed to fetch token metadata: {}", e);
            }
        }

        Err(anyhow!("Could not determine token age"))
    }

    /// Analyze individual token's bonding curve state using known bonding curve address
    async fn analyze_token_bonding_curve_with_address(&self, mint: &Pubkey, bonding_curve: &Pubkey) -> Result<Option<LiveTokenData>> {
        println!("     🔍 Using known bonding curve address: {}", bonding_curve);

        // Get bonding curve account data using the known address
        match self.rpc_client.get_account(bonding_curve) {
            Ok(account) => {
                // Parse bonding curve state
                let curve_state = BondingCurveState::from_account_data(&account.data)?;

                // Skip completed curves (already migrated)
                if curve_state.complete {
                    println!("     ❌ FILTER: Token already completed (migrated to Raydium)");
                    return Ok(None);
                }

                // Calculate progress and apply filters
                let progress = curve_state.progress_percentage();
                println!("     📊 Bonding curve progress: {:.2}%", progress * 100.0);

                // Apply basic survival filter
                if progress < self.min_progress {
                    println!("     ❌ FILTER: Progress {:.2}% < minimum {:.2}%",
                        progress * 100.0, self.min_progress * 100.0);
                    return Ok(None);
                }

                // Apply graduation potential filter
                if progress < self.sweet_spot_progress_min || progress > self.sweet_spot_progress_max {
                    println!("     ❌ FILTER: Progress {:.2}% outside sweet spot {:.2}%-{:.2}%",
                        progress * 100.0,
                        self.sweet_spot_progress_min * 100.0,
                        self.sweet_spot_progress_max * 100.0);
                    return Ok(None);
                }

                println!("     ✅ FILTER: Progress {:.2}% passed all filters", progress * 100.0);

                // Get additional token metadata from API
                if let Ok(Some(live_token)) = self.enrich_token_data_with_address(mint, &curve_state, bonding_curve).await {
                    return Ok(Some(live_token));
                }
            }
            Err(_) => {
                // Account doesn't exist or RPC error
                return Ok(None);
            }
        }

        Ok(None)
    }

    /// Enrich token data with API information using known bonding curve address
    async fn enrich_token_data_with_address(&self, mint: &Pubkey, curve_state: &BondingCurveState, bonding_curve: &Pubkey) -> Result<Option<LiveTokenData>> {
        // This would typically fetch from pump.fun API to get name, symbol, etc.
        // For now, create basic token data from bonding curve
        let progress = curve_state.progress_percentage();
        let price = curve_state.calculate_current_price();
        let liquidity_sol = curve_state.real_sol_reserves as f64 / 1e9;

        println!("     📊 Token metrics:");
        println!("       Progress: {:.4}%", progress * 100.0);
        println!("       Price: {:.10} SOL", price);
        println!("       Liquidity: {:.9} SOL", liquidity_sol);

        // Calculate market cap
        let token_supply = curve_state.token_total_supply as f64 / 1e6; // Assuming 6 decimals
        let market_cap_sol = price * token_supply;
        let market_cap_usd = market_cap_sol * 100.0; // Assuming $100 SOL

        println!("       Token supply: {:.0} tokens", token_supply);
        println!("       Market cap: {:.10} SOL = ${:.2} USD", market_cap_sol, market_cap_usd);

        // Apply market cap filter
        println!("     💰 Market cap: ${:.0} (range: ${:.0}-${:.0})",
            market_cap_usd, self.min_mcap, self.max_mcap);
        if market_cap_usd < self.min_mcap || market_cap_usd > self.max_mcap {
            println!("     ❌ FILTER: Market cap ${:.0} outside range ${:.0}-${:.0}",
                market_cap_usd, self.min_mcap, self.max_mcap);
            return Ok(None);
        }

        println!("     ✅ FILTER: Market cap ${:.0} passed filter", market_cap_usd);

        let live_token = LiveTokenData {
            mint: mint.to_string(),
            name: format!("Token_{}", &mint.to_string()[..8]), // Placeholder
            symbol: format!("TK{}", &mint.to_string()[..4]), // Placeholder
            description: "Bonding curve discovered token".to_string(),
            image: "".to_string(),
            creator: curve_state.creator.map(|c| c.to_string()).unwrap_or_default(),
            creation_time: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs() - (self.min_age_hours * 3600.0) as u64,
            market_cap_usd,
            liquidity_sol,
            price_sol: price,
            volume_24h: 0.0, // Would need to calculate from trade history
            holders: self.min_holders, // Placeholder
            transactions: self.min_trades, // Placeholder
            is_complete: curve_state.complete,
            bonding_curve: bonding_curve.to_string(),
            associated_bonding_curve: spl_associated_token_account::get_associated_token_address(bonding_curve, mint).to_string(),
        };

        Ok(Some(live_token))
    }

    /// Calculate buy signal using scoring system (from renovation guide)
    fn calculate_buy_signal(&self, token: &LiveTokenData) -> Option<TokenScore> {
        // Survival score: age + stability metrics
        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        let age_hours = (current_time - token.creation_time) as f64 / 3600.0;
        let survival_score = (age_hours / 24.0).min(1.0) as f32; // Normalize to 24 hours

        // Momentum score: trading velocity + holder growth
        let momentum_score = (token.transactions as f32 / 100.0).min(1.0); // Normalize to 100 trades

        // Graduation score: proximity to Raydium migration
        let progress = self.calculate_progress_from_liquidity(token.liquidity_sol);
        let graduation_score = if progress >= self.sweet_spot_progress_min && progress <= self.sweet_spot_progress_max {
            ((progress - self.sweet_spot_progress_min) / (self.sweet_spot_progress_max - self.sweet_spot_progress_min)) as f32
        } else {
            0.0
        };

        // Risk score: volatility + liquidity risks (inverse - lower is better)
        let liquidity_risk = if token.liquidity_sol < 50.0 { 0.8_f32 } else { 0.2_f32 };
        let mcap_risk = if token.market_cap_usd > 500000.0 { 0.3_f32 } else { 0.1_f32 };
        let risk_score = 1.0_f32 - (liquidity_risk + mcap_risk).min(1.0_f32);

        // Weighted total score
        let total_score = (survival_score * 0.25) + (momentum_score * 0.25) + (graduation_score * 0.35) + (risk_score * 0.15);

        Some(TokenScore {
            survival_score,
            momentum_score,
            graduation_score,
            risk_score,
            total_score,
        })
    }

    /// Helper: Calculate progress from liquidity (approximate)
    fn calculate_progress_from_liquidity(&self, liquidity_sol: f64) -> f64 {
        // Approximate progress based on SOL reserves (85 SOL = 100% graduation)
        (liquidity_sol / 85.0).min(1.0)
    }

    /// Helper: Derive bonding curve address
    fn derive_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {
        let pump_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
        Pubkey::find_program_address(&[b"bonding-curve", mint.as_ref()], &pump_program).0
    }

    /// Helper: Derive associated bonding curve address
    fn derive_associated_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {
        let bonding_curve = self.derive_bonding_curve_address(mint);
        spl_associated_token_account::get_associated_token_address(&bonding_curve, mint)
    }
}
