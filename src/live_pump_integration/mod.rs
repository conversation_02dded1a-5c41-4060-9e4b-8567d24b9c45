// Live pump.fun integration modules
pub mod filters;
pub mod graduation_checker;
pub mod websocket_monitor;
pub mod token_scanner;
pub mod periodic_scanner;
pub mod trade_executor;
pub mod position_monitor;
pub mod sell_logic;
pub mod dip_monitor;
pub mod enhanced_state_machine;
pub mod bonding_curve_monitor;
pub mod handoff_fix;
// pub mod chainstack_listener; // Temporarily disabled due to borrowing issues

// Re-export commonly used types and functions
pub use filters::TokenFilters;
pub use graduation_checker::{Grad<PERSON><PERSON><PERSON><PERSON>, GraduationCheck};
pub use websocket_monitor::WebSocketMonitor;
pub use token_scanner::TokenScanner;
pub use periodic_scanner::{PeriodicScanner, ScanStats};
pub use trade_executor::{TradeExecutor, BuyResult, SellResult};
pub use position_monitor::{PositionMonitor, MonitoringResult};
pub use sell_logic::{SellLogic, SellDecision, SellPriority};
pub use dip_monitor::{DipMoni<PERSON>, WatchedToken, DipCheckResult};
pub use enhanced_state_machine::{EnhancedStateMachine, EnhancedBotState};
pub use bonding_curve_monitor::{BondingCurveMonitor, TokenMetrics, TokenScore, BondingCurveState};
// pub use chainstack_listener::{ChainstackListener, ChainstackListenerConfig}; // Temporarily disabled

// Import the main integration struct and types from the original file
// This will be moved here in later phases
pub use crate::live_pump_integration_main::{
    LivePumpFunClient,
    LiveTokenData,
    TokenCreationEvent,
    TradeEvent,
};

// Import BondingCurveAccount from the correct module
pub use crate::pump_instruction_builder::BondingCurveAccount;

// Re-export the main client for backwards compatibility
pub use crate::live_pump_integration_main::LivePumpFunClient as Client;
