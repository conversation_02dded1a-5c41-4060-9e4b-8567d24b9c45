use anyhow::{Result, anyhow};
use solana_sdk::{pubkey::Pubkey, instruction::Instruction, system_instruction};
use solana_client::rpc_client::RpcClient;
use solana_sdk::signature::{Keypair, Signer};
use std::str::FromStr;

use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};
use crate::pump_instruction_builder::{PumpInstructionBuilder, BondingCurveAccount};
use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};
use crate::production_jito::{ProductionJitoClient, TradingPriority};
use crate::position_state_machine::ExitReason;
// TODO: Re-enable timing system once import issues are resolved
// use crate::transaction_timing::{TransactionTimer, TransactionType, TimingStatsTracker};
// use std::sync::{Arc, Mutex};

/// Trade execution engine for buy and sell operations
pub struct TradeExecutor {
    rpc_client: RpcClient,
    wallet_keypair: Keypair,
    trading_config: TradingConfig,
    priority_executor: Option<PriorityFeesExecutor>,
    jito_client: ProductionJitoClient,
    pump_instruction_builder: PumpInstructionBuilder,
    pump_fee_recipient: Pubkey,
    // TODO: Re-enable timing system
    // timing_stats: Arc<Mutex<TimingStatsTracker>>,
}

/// Result of a buy order execution
#[derive(Debug, Clone)]
pub struct BuyResult {
    pub amount_tokens: u64,
    pub amount_sol: f64,
    pub entry_price: f64,
    pub transaction_signature: String,
    pub confirmation_time_ms: u64,
    pub total_cost_lamports: u64,
}

/// Result of a sell order execution
#[derive(Debug, Clone)]
pub struct SellResult {
    pub amount_sol_received: f64,
    pub amount_tokens_sold: u64,
    pub exit_price: f64,
    pub transaction_signature: String,
    pub confirmation_time_ms: u64,
    pub total_fees_lamports: u64,
    pub exit_reason: ExitReason,
}

impl TradeExecutor {
    /// Create a new trade executor
    pub fn new(
        rpc_client: RpcClient,
        wallet_keypair: Keypair,
        trading_config: TradingConfig,
        priority_executor: Option<PriorityFeesExecutor>,
        jito_client: ProductionJitoClient,
        pump_instruction_builder: PumpInstructionBuilder,
        pump_fee_recipient: Pubkey,
    ) -> Self {
        Self {
            rpc_client,
            wallet_keypair,
            trading_config,
            priority_executor,
            jito_client,
            pump_instruction_builder,
            pump_fee_recipient,
            // TODO: Re-enable timing system
            // timing_stats: Arc::new(Mutex::new(TimingStatsTracker::new(100))), // Keep last 100 transactions
        }
    }

    /// Log trading events to file for historical analysis
    fn log_to_file(&self, message: &str) {
        use std::fs::OpenOptions;
        use std::io::Write;

        let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC");
        let log_entry = format!("[{}] {}\n", timestamp, message);

        if let Ok(mut file) = OpenOptions::new()
            .create(true)
            .append(true)
            .open("trading_logs.txt")
        {
            let _ = file.write_all(log_entry.as_bytes());
        }
    }

    /// Get timing statistics
    pub fn get_timing_statistics(&self) {
        // TODO: Re-enable timing system
        println!("📊 Transaction timing statistics temporarily disabled during development");
        // let stats = self.timing_stats.lock().unwrap();
        // stats.print_statistics();
    }

    /// Execute buy order for a token with priority fees or Jito MEV protection
    /// Returns BuyResult for position manager
    pub async fn execute_buy_order(&mut self, token: &LiveTokenData) -> Result<BuyResult> {
        println!("🚀 Executing buy order for {} ({})", token.name, token.symbol);

        // Calculate position size using environment variable percentage
        let wallet_balance = self.get_wallet_balance().await?;
        let position_size_percent = self.trading_config.base_position_percent / 100.0;
        let calculated_position_size = wallet_balance * position_size_percent;

        // Apply min/max trade amount limits from environment variables
        let mut position_size = calculated_position_size.max(self.trading_config.min_trade_amount);
        position_size = position_size.min(self.trading_config.max_trade_amount);

        // Debug position sizing calculation
        println!("🔍 POSITION SIZING DEBUG:");
        println!("   💰 Wallet Balance: {:.6} SOL", wallet_balance);
        println!("   📊 Position Percent: {:.1}%", self.trading_config.base_position_percent);
        println!("   🧮 Calculated Size: {:.6} SOL", calculated_position_size);
        println!("   📏 Min Trade Amount: {:.6} SOL", self.trading_config.min_trade_amount);
        println!("   📏 Max Trade Amount: {:.6} SOL", self.trading_config.max_trade_amount);
        println!("   🎯 Final Position Size: {:.6} SOL", position_size);
        println!("   📊 Current price: {:.9} SOL", token.price_sol);

        // Using priority fees only (Jito disabled)
        println!("⚡ Executing with Priority Fees...");
        self.execute_buy_with_priority_fees(token, position_size).await
    }

    /// Execute buy order using priority fees
    /// Returns BuyResult for position manager
    async fn execute_buy_with_priority_fees(&mut self, token: &LiveTokenData, position_size: f64) -> Result<BuyResult> {
        // TODO: Re-enable comprehensive timing
        let timing_log = format!("⏱️  Starting BUY transaction timing for {}", token.symbol);
        println!("{}", timing_log);
        self.log_to_file(&timing_log);
        let start_time = std::time::Instant::now();
        // let mut timer = TransactionTimer::start_transaction(
        //     TransactionType::Buy,
        //     token.symbol.clone(),
        //     token.mint.clone(),
        //     "priority_fees".to_string(),
        // );

        let mint = Pubkey::from_str(&token.mint)?;

        // Get bonding curve data to extract creator and reserves
        let (bonding_curve, _) = self.pump_instruction_builder.derive_bonding_curve(&mint);

        // Fetch bonding curve account to get creator and reserves
        let bonding_curve_account = match self.rpc_client.get_account(&bonding_curve) {
            Ok(account) => {
                println!("✅ Bonding curve account found, parsing data...");
                BondingCurveAccount::from_account_data(&account.data)?
            }
            Err(e) => {
                return Err(anyhow!("Failed to fetch bonding curve account: {}", e));
            }
        };

        let virtual_sol_reserves = bonding_curve_account.virtual_sol_reserves;
        let virtual_token_reserves = bonding_curve_account.virtual_token_reserves;
        let creator = bonding_curve_account.creator;

        println!("📊 Bonding Curve Data:");
        println!("   Virtual SOL Reserves: {} lamports ({:.6} SOL)",
            virtual_sol_reserves, virtual_sol_reserves as f64 / 1_000_000_000.0);
        println!("   Virtual Token Reserves: {}", virtual_token_reserves);
        println!("   Creator: {}", creator);
        println!("   Active: {}", bonding_curve_account.is_active());

        if !bonding_curve_account.is_active() {
            return Err(anyhow!("Bonding curve is complete (migrated to Raydium)"));
        }

        // Use calculated position size (not hardcoded test amount)
        let sol_cost_lamports = (position_size * 1_000_000_000.0) as u64;

        // Calculate expected tokens and max cost using actual position size
        let expected_tokens = self.pump_instruction_builder.calculate_tokens_out(
            position_size,
            virtual_sol_reserves,
            virtual_token_reserves
        );
        let max_sol_cost = self.pump_instruction_builder.calculate_max_sol_cost(sol_cost_lamports, 15.0); // 15% slippage for better execution

        println!("   🧮 Expected tokens: {}", expected_tokens);
        println!("   💰 Max SOL cost: {} lamports", max_sol_cost);

        // Always create ATA instruction (idempotent - won't fail if exists)
        let ata_instruction = Some(self.pump_instruction_builder.create_ata_instruction(
            &self.wallet_keypair.pubkey(),
            &self.wallet_keypair.pubkey(),
            &mint,
        ));

        // Use expected tokens directly (pump.fun expects base units, not decimal-adjusted)
        let token_amount = expected_tokens; // Use raw token amount from bonding curve calculation

        // Build buy instruction with creator for creator vault derivation
        let buy_instruction = self.pump_instruction_builder.build_buy_instruction(
            &self.wallet_keypair.pubkey(),
            &mint,
            token_amount,     // Token amount to buy (base units)
            max_sol_cost,     // Maximum SOL cost for slippage protection
            &creator,         // Pass creator for creator vault derivation
        )?;

        // TODO: Re-enable timing markers
        let instruction_time = start_time.elapsed();
        println!("⏱️  Instructions built in {}ms", instruction_time.as_millis());
        // timer.mark_instructions_built();

        // Execute with priority fees
        if let Some(ref priority_executor) = self.priority_executor {
            let submission_time = start_time.elapsed();
            println!("⏱️  Transaction submitted in {}ms", submission_time.as_millis());

            match priority_executor.execute_buy(buy_instruction, ata_instruction, &self.wallet_keypair).await {
                Ok(tx_result) if tx_result.success => {
                    let total_time = start_time.elapsed();
                    let completion_log = format!("⏱️  ✅ BUY TRANSACTION COMPLETE: Total time {}ms", total_time.as_millis());
                    println!("{}", completion_log);
                    self.log_to_file(&completion_log);

                    // TODO: Re-enable comprehensive timing
                    // let timing = timer.mark_confirmed(
                    //     true,
                    //     tx_result.signature.to_string(),
                    //     tx_result.retry_count,
                    //     tx_result.total_cost_lamports,
                    //     0, // No Jito tip for priority fees
                    // );
                    // {
                    //     let mut stats = self.timing_stats.lock().unwrap();
                    //     stats.add_timing(timing);
                    // }

                    println!("✅ Buy order executed successfully with Priority Fees!");
                    println!("   🪙 Tokens received: {}", expected_tokens);
                    println!("   📡 Transaction: {}", tx_result.signature);
                    println!("   ⚡ Confirmation time: {}ms", tx_result.confirmation_time_ms);
                    println!("   💰 Priority fee paid: {} lamports", tx_result.total_cost_lamports);

                    // Return data for position manager
                    Ok(BuyResult {
                        amount_tokens: expected_tokens,
                        amount_sol: position_size,
                        entry_price: token.price_sol,
                        transaction_signature: tx_result.signature.to_string(),
                        confirmation_time_ms: tx_result.confirmation_time_ms,
                        total_cost_lamports: tx_result.total_cost_lamports,
                    })
                }
                Ok(tx_result) => {
                    let total_time = start_time.elapsed();
                    println!("⏱️  ❌ BUY TRANSACTION FAILED: Total time {}ms", total_time.as_millis());

                    // TODO: Re-enable timing
                    Err(anyhow!("Priority fees transaction failed"))
                }
                Err(e) => {
                    let total_time = start_time.elapsed();
                    println!("⏱️  ❌ BUY TRANSACTION ERROR: Total time {}ms", total_time.as_millis());

                    // TODO: Re-enable timing
                    Err(anyhow!("Priority fees execution failed: {}", e))
                }
            }
        } else {
            Err(anyhow!("Priority fees executor not initialized"))
        }
    }

    /// Execute sell order for a specific token using comprehensive sell logic
    pub async fn execute_sell_for_token(&mut self, token_mint: &str, exit_reason: ExitReason, position_amount_tokens: u64) -> Result<SellResult> {
        println!("🚀 EXECUTING SELL ORDER: {} - {}", token_mint, exit_reason.description());
        println!("   💰 Selling {} tokens", position_amount_tokens);

        // TODO: Re-enable comprehensive timing for sell
        let timing_log = format!("⏱️  Starting SELL transaction timing for {}", &token_mint[..8]);
        println!("{}", timing_log);
        self.log_to_file(&timing_log);
        let start_time = std::time::Instant::now();
        // let mut timer = TransactionTimer::start_transaction(
        //     TransactionType::Sell,
        //     format!("Token_{}", &token_mint[..8]), // Use first 8 chars as symbol
        //     token_mint.to_string(),
        //     "priority_fees".to_string(),
        // );

        let mint = Pubkey::from_str(token_mint)?;

        // Get bonding curve data for sell calculation
        let (bonding_curve, _) = self.pump_instruction_builder.derive_bonding_curve(&mint);

        // Fetch bonding curve account to get current reserves and creator
        let bonding_curve_account = match self.rpc_client.get_account(&bonding_curve) {
            Ok(account) => {
                println!("✅ Bonding curve account found for sell");
                BondingCurveAccount::from_account_data(&account.data)?
            }
            Err(e) => {
                return Err(anyhow!("Failed to fetch bonding curve account for sell: {}", e));
            }
        };

        let virtual_sol_reserves = bonding_curve_account.virtual_sol_reserves;
        let virtual_token_reserves = bonding_curve_account.virtual_token_reserves;
        let creator = bonding_curve_account.creator;

        println!("📊 Bonding Curve Data for Sell:");
        println!("   Virtual SOL Reserves: {} lamports ({:.6} SOL)",
            virtual_sol_reserves, virtual_sol_reserves as f64 / 1_000_000_000.0);
        println!("   Virtual Token Reserves: {}", virtual_token_reserves);
        println!("   Creator: {}", creator);

        if !bonding_curve_account.is_active() {
            return Err(anyhow!("Cannot sell: Token has graduated to Raydium"));
        }

        // Calculate expected SOL output using bonding curve math
        let expected_sol_out = self.pump_instruction_builder.calculate_sol_out(
            position_amount_tokens,
            virtual_sol_reserves,
            virtual_token_reserves
        );

        // Apply slippage protection from environment variables
        let slippage_percent = std::env::var("PUMP_SLIPPAGE_PERCENT")
            .unwrap_or_else(|_| "20.0".to_string())
            .parse::<f64>()
            .unwrap_or(20.0);

        let min_sol_output = ((expected_sol_out as f64) * (1.0 - slippage_percent / 100.0)) as u64;

        println!("   🧮 Expected SOL out: {} lamports ({:.6} SOL)",
            expected_sol_out, expected_sol_out as f64 / 1_000_000_000.0);
        println!("   🛡️  Min SOL output ({}% slippage): {} lamports ({:.6} SOL)",
            slippage_percent, min_sol_output, min_sol_output as f64 / 1_000_000_000.0);

        // Build sell instruction
        let sell_instruction = self.pump_instruction_builder.build_sell_instruction(
            &self.wallet_keypair.pubkey(),
            &mint,
            position_amount_tokens,
            min_sol_output,
            &creator,
        )?;

        // TODO: Re-enable timing markers
        let instruction_time = start_time.elapsed();
        println!("⏱️  Sell instructions built in {}ms", instruction_time.as_millis());

        // Execute sell with priority fees
        let submission_time = start_time.elapsed();
        println!("⏱️  Sell transaction submitted in {}ms", submission_time.as_millis());
        if let Some(ref priority_executor) = self.priority_executor {
            match priority_executor.execute_sell(sell_instruction, &self.wallet_keypair).await {
                Ok(tx_result) if tx_result.success => {
                    let total_time = start_time.elapsed();
                    let completion_log = format!("⏱️  ✅ SELL TRANSACTION COMPLETE: Total time {}ms", total_time.as_millis());
                    println!("{}", completion_log);
                    self.log_to_file(&completion_log);

                    // TODO: Re-enable comprehensive timing
                    // let timing = timer.mark_confirmed(
                    //     true,
                    //     tx_result.signature.to_string(),
                    //     tx_result.retry_count,
                    //     tx_result.total_cost_lamports,
                    //     0, // No Jito tip for priority fees
                    // );
                    // {
                    //     let mut stats = self.timing_stats.lock().unwrap();
                    //     stats.add_timing(timing);
                    // }

                    let actual_sol_received = expected_sol_out as f64 / 1_000_000_000.0; // Convert to SOL
                    let exit_price = actual_sol_received / (position_amount_tokens as f64);

                    println!("✅ Sell order executed successfully with Priority Fees!");
                    println!("   💰 SOL received: {:.6} SOL", actual_sol_received);
                    println!("   📡 Transaction: {}", tx_result.signature);
                    println!("   ⚡ Confirmation time: {}ms", tx_result.confirmation_time_ms);
                    println!("   💸 Priority fee paid: {} lamports", tx_result.total_cost_lamports);
                    println!("   🎯 Exit reason: {}", exit_reason.description());

                    Ok(SellResult {
                        amount_sol_received: actual_sol_received,
                        amount_tokens_sold: position_amount_tokens,
                        exit_price,
                        transaction_signature: tx_result.signature.to_string(),
                        confirmation_time_ms: tx_result.confirmation_time_ms,
                        total_fees_lamports: tx_result.total_cost_lamports,
                        exit_reason,
                    })
                }
                Ok(tx_result) => {
                    let total_time = start_time.elapsed();
                    println!("⏱️  ❌ SELL TRANSACTION FAILED: Total time {}ms", total_time.as_millis());

                    // TODO: Re-enable timing
                    Err(anyhow!("Priority fees sell transaction failed"))
                }
                Err(e) => {
                    let total_time = start_time.elapsed();
                    println!("⏱️  ❌ SELL TRANSACTION ERROR: Total time {}ms", total_time.as_millis());

                    // TODO: Re-enable timing
                    Err(anyhow!("Priority fees sell execution failed: {}", e))
                }
            }
        } else {
            Err(anyhow!("Priority fees executor not initialized"))
        }
    }

    /// Get current wallet balance
    async fn get_wallet_balance(&self) -> Result<f64> {
        let balance = self.rpc_client.get_balance(&self.wallet_keypair.pubkey())?;
        Ok(balance as f64 / 1_000_000_000.0) // Convert lamports to SOL
    }

    /// Get current token price from bonding curve
    async fn get_current_token_price(&self, token_mint: &str) -> Result<f64> {
        // Use the extracted graduation checker module
        crate::live_pump_integration::GraduationChecker::get_current_token_price(
            token_mint,
            &self.rpc_client,
            &self.pump_instruction_builder,
        ).await
    }

    /// Create pump.fun buy instruction (legacy method)
    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {
        let _mint_pubkey = Pubkey::from_str(&token.mint)?;
        let _bonding_curve = Pubkey::from_str(&token.bonding_curve)?;
        let _associated_bonding_curve = Pubkey::from_str(&token.associated_bonding_curve)?;

        // This is a simplified instruction - in production you'd need the full pump.fun instruction format
        Ok(system_instruction::transfer(
            &self.wallet_keypair.pubkey(),
            &self.pump_fee_recipient,
            (amount_sol * 1_000_000_000.0) as u64, // Convert to lamports
        ))
    }

    /// Execute buy order using Jito (fallback method) - DEPRECATED
    /// Returns BuyResult for position manager
    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<BuyResult> {
        // Create simplified buy instruction for Jito
        let buy_instruction = self.create_pump_buy_instruction(token, position_size).await?;

        match self.jito_client.execute_pump_trade(
            buy_instruction,
            &self.wallet_keypair,
            TradingPriority::High,
        ).await {
            Ok(trade_result) if trade_result.success => {
                let tokens_received = (position_size / token.price_sol) as u64;

                println!("✅ Buy order executed successfully with Jito!");
                println!("   🪙 Tokens received: {}", tokens_received);
                println!("   🎯 Bundle ID: {}", trade_result.bundle_id);
                println!("   💰 Tip paid: {} lamports", trade_result.tip_paid);

                // Return data for position manager
                Ok(BuyResult {
                    amount_tokens: tokens_received,
                    amount_sol: position_size,
                    entry_price: token.price_sol,
                    transaction_signature: trade_result.bundle_id,
                    confirmation_time_ms: 0, // Jito doesn't provide this
                    total_cost_lamports: trade_result.tip_paid,
                })
            }
            _ => {
                Err(anyhow!("Jito buy execution failed"))
            }
        }
    }
}
