//! Blockchain Trading Bot Library
//!
//! This library provides modules for the pump.fun trading bot,
//! including external dashboard functionality that can be used
//! independently of the main trading logic.

// Core trading modules
pub mod production_jito;
pub mod live_pump_integration;
pub mod live_pump_integration_main;
pub mod performance_dashboard;
pub mod mev_protection;
pub mod pump_instruction_builder;
pub mod priority_fees_executor;
pub mod pump_api_client;
pub mod position_state_machine;
pub mod position_manager;
pub mod chainstack_client;
pub mod chainstack_platform_client;
pub mod helius_client;
pub mod shyft_client;
pub mod logs_event_processor;
pub mod logs_listener;
pub mod websocket_tester;
pub mod token_aging_pipeline;
pub mod web_dashboard;

// External dashboard modules (completely independent of trading logic)
pub mod dexscreener_client;
pub mod external_dashboard;
