#!/bin/bash

# Start trading bot with comprehensive logging
# This script captures all output to both console and log files

echo "🚀 Starting Pump.fun Trading Bot with Comprehensive Logging"
echo "==========================================================="

# Create logs directory if it doesn't exist
mkdir -p logs

# Get current timestamp for log file naming
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/trading_session_${TIMESTAMP}.log"

echo "📝 Logging to: $LOG_FILE"
echo "📊 Trading logs will also be written to: trading_logs.txt"
echo ""

# Start the bot with tee to capture output to both console and file
cargo run 2>&1 | tee "$LOG_FILE"

echo ""
echo "🏁 Trading session ended"
echo "📁 Session log saved to: $LOG_FILE"
echo "📁 Trading events logged to: trading_logs.txt"
