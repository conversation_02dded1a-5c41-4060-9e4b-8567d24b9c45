# =============================================================================
# ENHANCED DASHBOARD CONFIGURATION
# =============================================================================
# Configuration for the Enhanced Trading Dashboard with DexScreener integration
# Copy these settings to your .env file or create a separate .env.enhanced file

# =============================================================================
# DASHBOARD SERVER SETTINGS
# =============================================================================

# Port for the enhanced dashboard (different from external dashboard)
ENHANCED_DASHBOARD_PORT=42070

# Refresh interval in seconds (how often to fetch new data from DexScreener)
ENHANCED_DASHBOARD_REFRESH_SECONDS=30

# Maximum number of tokens to display
ENHANCED_DASHBOARD_MAX_TOKENS=20

# =============================================================================
# TRACKED TOKEN ADDRESSES
# =============================================================================
# Comma-separated list of Solana token addresses to track
# These should be the tokens your bot is trading or monitoring

# Example with popular Solana tokens (replace with your actual tracked tokens)
ENHANCED_DASHBOARD_TRACKED_TOKENS=So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So

# Example with pump.fun tokens (replace with actual addresses from your bot)
# ENHANCED_DASHBOARD_TRACKED_TOKENS=Pjr1ZTAfatZmuP7rKce4n7f5NTanV5uEGNtqvfGpump,8x7K9nQjP2mF3vL4wR6tS5uE1cY9bN3pA2qD7fG8hJ6,5A3bC8dE2fG9hI1jK4lM6nO7pQ8rS9tU0vW1xY2zA3b

# =============================================================================
# DEXSCREENER API SETTINGS
# =============================================================================
# DexScreener API doesn't require authentication, but you can configure rate limiting

# Request timeout in seconds
DEXSCREENER_TIMEOUT_SECONDS=30

# User agent for API requests
DEXSCREENER_USER_AGENT=Mozilla/5.0 (compatible; PumpBot-Dashboard/1.0)

# =============================================================================
# DASHBOARD FEATURES
# =============================================================================

# Enable/disable specific dashboard features
ENHANCED_DASHBOARD_SHOW_PRICE_CHARTS=true
ENHANCED_DASHBOARD_SHOW_TRADING_STATS=true
ENHANCED_DASHBOARD_SHOW_MARKET_DATA=true
ENHANCED_DASHBOARD_SHOW_DEXSCREENER_LINKS=true

# Color theme (dark/light)
ENHANCED_DASHBOARD_THEME=dark

# =============================================================================
# INTEGRATION WITH MAIN BOT
# =============================================================================
# If you want to automatically track tokens that your main bot is trading,
# you can enable this integration (requires additional development)

# Enable automatic token discovery from bot
ENHANCED_DASHBOARD_AUTO_DISCOVER_TOKENS=false

# Path to bot's token tracking file (if available)
# ENHANCED_DASHBOARD_BOT_TOKENS_FILE=./tracked_tokens.json

# =============================================================================
# EXAMPLE USAGE
# =============================================================================
# 
# To run the enhanced dashboard:
# 1. Copy this file to .env.enhanced
# 2. Update ENHANCED_DASHBOARD_TRACKED_TOKENS with your token addresses
# 3. Run: cargo run --bin enhanced_dashboard
# 4. Open: http://localhost:42070
#
# To run with custom settings:
# ENHANCED_DASHBOARD_PORT=8080 cargo run --bin enhanced_dashboard
#
# =============================================================================
# TRACKED TOKEN EXAMPLES
# =============================================================================

# Popular Solana tokens for testing:
# So11111111111111111111111111111111111111112  # Wrapped SOL
# EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v  # USDC
# mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So  # Marinade SOL
# 7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs  # Ethereum (Wormhole)
# 9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E  # Wrapped Bitcoin

# Pump.fun tokens (examples - replace with actual addresses):
# Pjr1ZTAfatZmuP7rKce4n7f5NTanV5uEGNtqvfGpump  # Example pump token 1
# 8x7K9nQjP2mF3vL4wR6tS5uE1cY9bN3pA2qD7fG8hJ6  # Example pump token 2
# 5A3bC8dE2fG9hI1jK4lM6nO7pQ8rS9tU0vW1xY2zA3b  # Example pump token 3

# =============================================================================
# DASHBOARD FEATURES OVERVIEW
# =============================================================================
#
# The Enhanced Dashboard provides:
#
# 1. REAL-TIME MARKET DATA
#    - Current prices in USD and SOL
#    - 24h price changes with visual indicators
#    - Market cap and fully diluted valuation
#    - Liquidity data from DexScreener
#
# 2. TRADING ACTIVITY
#    - 24h trading volume
#    - Number of transactions (buys/sells)
#    - Buy/sell ratio analysis
#    - Trading pair information
#
# 3. VISUAL INTERFACE
#    - Responsive grid layout
#    - Color-coded price changes (green/red)
#    - Real-time updates every 30 seconds
#    - Direct links to DexScreener
#
# 4. SUMMARY STATISTICS
#    - Total tracked tokens
#    - Average price change across portfolio
#    - Total market cap and volume
#    - Aggregated trading metrics
#
# =============================================================================
