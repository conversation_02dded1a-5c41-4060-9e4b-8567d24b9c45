[package]
name = "blockchain-bot"
version = "0.1.0"
edition = "2021"
description = "Standalone pump.fun blockchain trading bot"
default-run = "blockchain-bot"

[lib]
name = "blockchain_bot"
path = "src/lib.rs"

[workspace]

[[bin]]
name = "blockchain-bot"
path = "src/main.rs"

[[bin]]
name = "websocket-test"
path = "src/bin/websocket_test.rs"


[[bin]]
name = "test-jito-direct"
path = "test_jito_direct.rs"

[[bin]]
name = "test-jito-encoding"
path = "test_jito_encoding.rs"

[[bin]]
name = "test-shyft-client"
path = "test_shyft_client.rs"

[[bin]]
name = "external_dashboard"
path = "src/bin/external_dashboard.rs"

[dependencies]
# Solana Core Dependencies
solana-client = "1.18"
solana-sdk = "1.18"
solana-account-decoder = "1.18"
solana-transaction-status = "1.18"

# SPL Token Support
spl-token = "4.0"
spl-associated-token-account = "2.3"

# Anchor Framework (for pump.fun program interaction)
anchor-client = "0.29"
anchor-lang = "0.29"

# Async Runtime
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = { version = "0.21", features = ["native-tls"] }

# HTTP Client for API calls
reqwest = { version = "0.11", features = ["json"] }

# gRPC and protobuf for future Shyft streaming (disabled due to version conflicts)
# tonic = "0.11"
# prost = "0.12"
# yellowstone-grpc-client = "1.18"
# yellowstone-grpc-proto = "1.18"
futures-util = "0.3"

# JSON Handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Base58 Encoding/Decoding
bs58 = "0.5"
base64 = "0.21"
hex = "0.4"

# Error Handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# Configuration
dotenv = "0.15"

# Time Handling
chrono = { version = "0.4", features = ["serde"] }

# Cryptography (using compatible version)
# ed25519-dalek = "1.0"  # Commented out to avoid conflicts

# WebSocket for real-time data (already included above with version 0.21)
url = "2.5"

# Random number generation for testing
rand = "0.8"

# Jito MEV Protection
bincode = "1.3"

# Web Dashboard
warp = "0.3"
webbrowser = "0.8"
