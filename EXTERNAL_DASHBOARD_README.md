# External Trading Dashboard

A standalone web dashboard that displays your trading activity by fetching data from external sources like DexScreener, completely independent of the bot's internal trading logic.

## Features

- 📊 **External Data Source**: Fetches trading data from DexScreener API
- 🔄 **Real-time Updates**: Automatically refreshes every 30 seconds
- 📈 **Two-Column Layout**: Recent buys and sells displayed side by side
- 🎯 **Last 10 Trades**: Shows your most recent trading activity in 24h
- 🌐 **Web Interface**: Clean, responsive dashboard on localhost:42069
- 🚀 **Independent Operation**: Runs separately from trading bot

## Quick Start

### 1. Set Your Wallet Address

```bash
export PUMP_WALLET_ADDRESS=GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus
```

### 2. Run the Dashboard

```bash
cargo run --bin external_dashboard
```

### 3. Access the Dashboard

Open your browser to: http://localhost:42069

## Configuration

The dashboard can be configured using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `PUMP_WALLET_ADDRESS` | Your Solana wallet address | `GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus` |
| `DASHBOARD_PORT` | Web server port | `42069` |
| `DASHBOARD_REFRESH_INTERVAL` | Refresh interval in seconds | `30` |
| `DASHBOARD_MAX_TRADES` | Maximum trades to display | `10` |

### Example with Custom Configuration

```bash
PUMP_WALLET_ADDRESS="YourWalletAddress" \
DASHBOARD_PORT="8080" \
DASHBOARD_REFRESH_INTERVAL="60" \
DASHBOARD_MAX_TRADES="20" \
cargo run --bin external_dashboard
```

## Dashboard Features

### Summary Cards
- **Total Trades**: All-time trading count
- **24h Trades**: Trades in the last 24 hours
- **24h P&L (SOL)**: Profit/Loss in SOL
- **24h P&L (USD)**: Profit/Loss in USD
- **Win Rate**: Success percentage for 24h

### Trade Columns
- **Left Column**: Recent buy transactions
- **Right Column**: Recent sell transactions
- **Click to View**: Click any trade to open DexScreener page

### Real-time Status
- **Live Data Indicator**: Shows connection status
- **Last Updated**: Timestamp of last data refresh
- **Auto-refresh**: Updates every 30 seconds (configurable)

## Data Source

The dashboard fetches data from DexScreener's API to provide:
- Trade history for your wallet
- Token prices and market data
- Transaction timestamps and amounts
- Direct links to DexScreener pages

## Independence from Trading Bot

This dashboard is completely separate from the trading bot:
- ✅ **No interference** with trading logic
- ✅ **Independent process** - can run while bot is trading
- ✅ **External data** - doesn't rely on bot's internal state
- ✅ **Safe operation** - read-only data display

## Troubleshooting

### Port Already in Use
If port 42069 is busy:
```bash
DASHBOARD_PORT="8080" cargo run --bin external_dashboard
```

### No Trading Data
- Verify your wallet address is correct
- Check that you have recent trades on DexScreener
- Ensure internet connection is working

### Dashboard Not Loading
- Check the console output for error messages
- Verify the port is accessible
- Try refreshing the browser page

## Development

### Project Structure
```
src/
├── bin/
│   └── external_dashboard.rs    # Standalone dashboard binary
├── dexscreener_client.rs        # DexScreener API client
├── external_dashboard.rs        # Dashboard server logic
└── main.rs                      # Main trading bot (separate)

external_dashboard.html          # Dashboard web interface
```

### API Endpoints
- `GET /` - Dashboard web interface
- `GET /api/dashboard` - JSON data for dashboard
- `GET /health` - Health check endpoint

## Example DexScreener URL

The dashboard works with DexScreener URLs like:
```
https://dexscreener.com/solana/61VMwkXR4GkoEm6nb5ZFaA7Rw71PHDt8gVA3zu5AuDTS?maker=GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus
```

Where:
- `61VMwkXR4GkoEm6nb5ZFaA7Rw71PHDt8gVA3zu5AuDTS` is the token address
- `GnmQCBEQpn48ysFVf8D2JCJVkDssgUYtvdKS7skNEfus` is your wallet address

## License

This dashboard is part of the pump.fun trading bot project and follows the same license terms.
