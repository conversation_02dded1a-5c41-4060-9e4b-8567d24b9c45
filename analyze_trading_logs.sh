#!/bin/bash

# Analyze trading logs to extract timing information
echo "📊 TRADING LOG ANALYSIS"
echo "======================="

# Check if trading_logs.txt exists
if [ ! -f "trading_logs.txt" ]; then
    echo "❌ No trading_logs.txt file found"
    echo "💡 Run the bot first to generate trading logs"
    exit 1
fi

echo "📁 Analyzing trading_logs.txt..."
echo ""

# Extract buy transaction timings
echo "🟢 BUY TRANSACTION TIMINGS:"
echo "=========================="
grep "Starting BUY transaction timing" trading_logs.txt | tail -10
echo ""
grep "BUY TRANSACTION COMPLETE" trading_logs.txt | tail -10
echo ""

# Extract sell transaction timings  
echo "🔴 SELL TRANSACTION TIMINGS:"
echo "==========================="
grep "Starting SELL transaction timing" trading_logs.txt | tail -10
echo ""
grep "SELL TRANSACTION COMPLETE" trading_logs.txt | tail -10
echo ""

# Extract position sizing debug info
echo "💰 POSITION SIZING DEBUG:"
echo "========================"
grep -A 7 "POSITION SIZING DEBUG" trading_logs.txt | tail -20
echo ""

# Show recent trading activity
echo "📈 RECENT TRADING ACTIVITY (Last 20 entries):"
echo "=============================================="
tail -20 trading_logs.txt
echo ""

# Count transactions
BUY_COUNT=$(grep -c "Starting BUY transaction timing" trading_logs.txt)
SELL_COUNT=$(grep -c "Starting SELL transaction timing" trading_logs.txt)
BUY_COMPLETE_COUNT=$(grep -c "BUY TRANSACTION COMPLETE" trading_logs.txt)
SELL_COMPLETE_COUNT=$(grep -c "SELL TRANSACTION COMPLETE" trading_logs.txt)

echo "📊 TRANSACTION SUMMARY:"
echo "======================"
echo "Buy transactions initiated: $BUY_COUNT"
echo "Buy transactions completed: $BUY_COMPLETE_COUNT"
echo "Sell transactions initiated: $SELL_COUNT"
echo "Sell transactions completed: $SELL_COMPLETE_COUNT"
echo ""

if [ $BUY_COUNT -gt 0 ]; then
    echo "✅ Trading activity detected!"
else
    echo "⚠️  No trading activity found in logs"
fi
