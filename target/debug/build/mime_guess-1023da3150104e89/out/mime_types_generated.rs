static REV_MAPPINGS: &'static [(UniCase<&'static str>, TopLevelExts)] = &[(UniCase::ascii("application"), TopLevelExts { start: 0, end: 941, subs: &[(UniCase::ascii("acad"), (0, 1)),(UniCase::ascii("andrew-inset"), (1, 2)),(UniCase::ascii("annodex"), (2, 3)),(UniCase::ascii("applixware"), (3, 4)),(UniCase::ascii("atom+xml"), (4, 5)),(UniCase::ascii("atomcat+xml"), (5, 6)),(UniCase::ascii("atomsvc+xml"), (6, 7)),(UniCase::ascii("bdoc"), (7, 8)),(UniCase::ascii("ccxml+xml"), (8, 9)),(UniCase::ascii("cdmi-capability"), (9, 10)),(UniCase::ascii("cdmi-container"), (10, 11)),(UniCase::ascii("cdmi-domain"), (11, 12)),(UniCase::ascii("cdmi-object"), (12, 13)),(UniCase::ascii("cdmi-queue"), (13, 14)),(UniCase::ascii("cu-seeme"), (14, 15)),(UniCase::ascii("dash+xml"), (15, 16)),(UniCase::ascii("davmount+xml"), (16, 17)),(UniCase::ascii("directx"), (17, 18)),(UniCase::ascii("docbook+xml"), (18, 19)),(UniCase::ascii("dssc+der"), (19, 20)),(UniCase::ascii("dssc+xml"), (20, 21)),(UniCase::ascii("emma+xml"), (21, 22)),(UniCase::ascii("envoy"), (22, 23)),(UniCase::ascii("epub+zip"), (23, 24)),(UniCase::ascii("etl"), (24, 25)),(UniCase::ascii("exi"), (25, 26)),(UniCase::ascii("font-sfnt"), (26, 28)),(UniCase::ascii("font-tdpfr"), (28, 29)),(UniCase::ascii("font-woff"), (29, 30)),(UniCase::ascii("fractals"), (30, 31)),(UniCase::ascii("fsharp-script"), (31, 33)),(UniCase::ascii("futuresplash"), (33, 34)),(UniCase::ascii("geo+json"), (34, 35)),(UniCase::ascii("gml+xml"), (35, 36)),(UniCase::ascii("gpx+xml"), (36, 37)),(UniCase::ascii("gxf"), (37, 38)),(UniCase::ascii("gzip"), (38, 39)),(UniCase::ascii("hjson"), (39, 40)),(UniCase::ascii("hta"), (40, 41)),(UniCase::ascii("hyperstudio"), (41, 42)),(UniCase::ascii("inkml+xml"), (42, 44)),(UniCase::ascii("internet-property-stream"), (44, 45)),(UniCase::ascii("ipfix"), (45, 46)),(UniCase::ascii("java-archive"), (46, 49)),(UniCase::ascii("java-serialized-object"), (49, 50)),(UniCase::ascii("javascript"), (50, 51)),(UniCase::ascii("json"), (51, 52)),(UniCase::ascii("json5"), (52, 53)),(UniCase::ascii("jsonml+json"), (53, 54)),(UniCase::ascii("ld+json"), (54, 55)),(UniCase::ascii("liquidmotion"), (55, 57)),(UniCase::ascii("lost+xml"), (57, 58)),(UniCase::ascii("mac-binhex40"), (58, 59)),(UniCase::ascii("mac-compactpro"), (59, 60)),(UniCase::ascii("mads+xml"), (60, 61)),(UniCase::ascii("manifest+json"), (61, 62)),(UniCase::ascii("marc"), (62, 63)),(UniCase::ascii("marcxml+xml"), (63, 64)),(UniCase::ascii("mathematica"), (64, 67)),(UniCase::ascii("mathml+xml"), (67, 68)),(UniCase::ascii("mbox"), (68, 69)),(UniCase::ascii("mediaservercontrol+xml"), (69, 70)),(UniCase::ascii("metalink+xml"), (70, 71)),(UniCase::ascii("metalink4+xml"), (71, 72)),(UniCase::ascii("mets+xml"), (72, 73)),(UniCase::ascii("mods+xml"), (73, 74)),(UniCase::ascii("mp21"), (74, 76)),(UniCase::ascii("mp4"), (76, 77)),(UniCase::ascii("mpeg"), (77, 78)),(UniCase::ascii("ms-vsi"), (78, 79)),(UniCase::ascii("msaccess"), (79, 86)),(UniCase::ascii("msaccess.addin"), (86, 87)),(UniCase::ascii("msaccess.cab"), (87, 88)),(UniCase::ascii("msaccess.ftemplate"), (88, 89)),(UniCase::ascii("msaccess.runtime"), (89, 90)),(UniCase::ascii("msaccess.webapplication"), (90, 91)),(UniCase::ascii("msword"), (91, 95)),(UniCase::ascii("mxf"), (95, 96)),(UniCase::ascii("n-quads"), (96, 97)),(UniCase::ascii("n-triples"), (97, 98)),(UniCase::ascii("octet-stream"), (98, 169)),(UniCase::ascii("oda"), (169, 170)),(UniCase::ascii("oebps-package+xml"), (170, 171)),(UniCase::ascii("ogg"), (171, 172)),(UniCase::ascii("olescript"), (172, 173)),(UniCase::ascii("omdoc+xml"), (173, 174)),(UniCase::ascii("onenote"), (174, 180)),(UniCase::ascii("opensearchdescription+xml"), (180, 181)),(UniCase::ascii("oxps"), (181, 182)),(UniCase::ascii("patch-ops-error+xml"), (182, 183)),(UniCase::ascii("pdf"), (183, 184)),(UniCase::ascii("pgp-encrypted"), (184, 185)),(UniCase::ascii("pgp-signature"), (185, 187)),(UniCase::ascii("pics-rules"), (187, 188)),(UniCase::ascii("pkcs10"), (188, 189)),(UniCase::ascii("pkcs7-mime"), (189, 191)),(UniCase::ascii("pkcs7-signature"), (191, 192)),(UniCase::ascii("pkcs8"), (192, 193)),(UniCase::ascii("pkix-attr-cert"), (193, 194)),(UniCase::ascii("pkix-crl"), (194, 195)),(UniCase::ascii("pkix-pkipath"), (195, 196)),(UniCase::ascii("pkixcmp"), (196, 197)),(UniCase::ascii("postscript"), (197, 200)),(UniCase::ascii("PowerShell"), (200, 201)),(UniCase::ascii("prs.cww"), (201, 202)),(UniCase::ascii("pskc+xml"), (202, 203)),(UniCase::ascii("raml+yaml"), (203, 204)),(UniCase::ascii("rat-file"), (204, 205)),(UniCase::ascii("rdf+xml"), (205, 206)),(UniCase::ascii("reginfo+xml"), (206, 207)),(UniCase::ascii("relax-ng-compact-syntax"), (207, 208)),(UniCase::ascii("resource-lists+xml"), (208, 209)),(UniCase::ascii("resource-lists-diff+xml"), (209, 210)),(UniCase::ascii("rpki-ghostbusters"), (210, 211)),(UniCase::ascii("rpki-manifest"), (211, 212)),(UniCase::ascii("rpki-roa"), (212, 213)),(UniCase::ascii("rsd+xml"), (213, 214)),(UniCase::ascii("rss+xml"), (214, 215)),(UniCase::ascii("rtf"), (215, 216)),(UniCase::ascii("sbml+xml"), (216, 217)),(UniCase::ascii("scvp-cv-request"), (217, 218)),(UniCase::ascii("scvp-cv-response"), (218, 219)),(UniCase::ascii("scvp-vp-request"), (219, 220)),(UniCase::ascii("scvp-vp-response"), (220, 221)),(UniCase::ascii("sdp"), (221, 222)),(UniCase::ascii("set-payment-initiation"), (222, 223)),(UniCase::ascii("set-registration-initiation"), (223, 224)),(UniCase::ascii("shf+xml"), (224, 225)),(UniCase::ascii("smil+xml"), (225, 226)),(UniCase::ascii("sparql-query"), (226, 227)),(UniCase::ascii("sparql-results+xml"), (227, 228)),(UniCase::ascii("srgs"), (228, 229)),(UniCase::ascii("srgs+xml"), (229, 230)),(UniCase::ascii("sru+xml"), (230, 231)),(UniCase::ascii("ssdl+xml"), (231, 232)),(UniCase::ascii("ssml+xml"), (232, 233)),(UniCase::ascii("step"), (233, 235)),(UniCase::ascii("streamingmedia"), (235, 236)),(UniCase::ascii("tei+xml"), (236, 238)),(UniCase::ascii("thraud+xml"), (238, 239)),(UniCase::ascii("timestamped-data"), (239, 240)),(UniCase::ascii("trig"), (240, 241)),(UniCase::ascii("vnd.3gpp.pic-bw-large"), (241, 242)),(UniCase::ascii("vnd.3gpp.pic-bw-small"), (242, 243)),(UniCase::ascii("vnd.3gpp.pic-bw-var"), (243, 244)),(UniCase::ascii("vnd.3gpp2.tcap"), (244, 245)),(UniCase::ascii("vnd.3m.post-it-notes"), (245, 246)),(UniCase::ascii("vnd.accpac.simply.aso"), (246, 247)),(UniCase::ascii("vnd.accpac.simply.imp"), (247, 248)),(UniCase::ascii("vnd.acucobol"), (248, 249)),(UniCase::ascii("vnd.acucorp"), (249, 251)),(UniCase::ascii("vnd.adobe.air-application-installer-package+zip"), (251, 252)),(UniCase::ascii("vnd.adobe.formscentral.fcdt"), (252, 253)),(UniCase::ascii("vnd.adobe.fxp"), (253, 255)),(UniCase::ascii("vnd.adobe.xdp+xml"), (255, 256)),(UniCase::ascii("vnd.adobe.xfdf"), (256, 257)),(UniCase::ascii("vnd.ahead.space"), (257, 258)),(UniCase::ascii("vnd.airzip.filesecure.azf"), (258, 259)),(UniCase::ascii("vnd.airzip.filesecure.azs"), (259, 260)),(UniCase::ascii("vnd.amazon.ebook"), (260, 261)),(UniCase::ascii("vnd.americandynamics.acc"), (261, 262)),(UniCase::ascii("vnd.amiga.ami"), (262, 263)),(UniCase::ascii("vnd.android.package-archive"), (263, 264)),(UniCase::ascii("vnd.anser-web-certificate-issue-initiation"), (264, 265)),(UniCase::ascii("vnd.anser-web-funds-transfer-initiation"), (265, 266)),(UniCase::ascii("vnd.antix.game-component"), (266, 267)),(UniCase::ascii("vnd.apache.parquet"), (267, 268)),(UniCase::ascii("vnd.apple.installer+xml"), (268, 269)),(UniCase::ascii("vnd.apple.pkpass"), (269, 270)),(UniCase::ascii("vnd.aristanetworks.swi"), (270, 271)),(UniCase::ascii("vnd.astraea-software.iota"), (271, 272)),(UniCase::ascii("vnd.audiograph"), (272, 273)),(UniCase::ascii("vnd.blueice.multipass"), (273, 274)),(UniCase::ascii("vnd.bmi"), (274, 275)),(UniCase::ascii("vnd.businessobjects"), (275, 276)),(UniCase::ascii("vnd.chemdraw+xml"), (276, 277)),(UniCase::ascii("vnd.chipnuts.karaoke-mmd"), (277, 278)),(UniCase::ascii("vnd.cinderella"), (278, 279)),(UniCase::ascii("vnd.citationstyles.style+xml"), (279, 280)),(UniCase::ascii("vnd.claymore"), (280, 281)),(UniCase::ascii("vnd.cloanto.rp9"), (281, 282)),(UniCase::ascii("vnd.clonk.c4group"), (282, 287)),(UniCase::ascii("vnd.cluetrust.cartomobile-config"), (287, 288)),(UniCase::ascii("vnd.cluetrust.cartomobile-config-pkg"), (288, 289)),(UniCase::ascii("vnd.commonspace"), (289, 290)),(UniCase::ascii("vnd.contact.cmsg"), (290, 291)),(UniCase::ascii("vnd.cosmocaller"), (291, 292)),(UniCase::ascii("vnd.crick.clicker"), (292, 293)),(UniCase::ascii("vnd.crick.clicker.keyboard"), (293, 294)),(UniCase::ascii("vnd.crick.clicker.palette"), (294, 295)),(UniCase::ascii("vnd.crick.clicker.template"), (295, 296)),(UniCase::ascii("vnd.crick.clicker.wordbank"), (296, 297)),(UniCase::ascii("vnd.criticaltools.wbs+xml"), (297, 298)),(UniCase::ascii("vnd.cups-ppd"), (298, 299)),(UniCase::ascii("vnd.curl.car"), (299, 300)),(UniCase::ascii("vnd.curl.pcurl"), (300, 301)),(UniCase::ascii("vnd.dart"), (301, 302)),(UniCase::ascii("vnd.data-vision.rdz"), (302, 303)),(UniCase::ascii("vnd.dece.data"), (303, 307)),(UniCase::ascii("vnd.dece.ttml+xml"), (307, 309)),(UniCase::ascii("vnd.dece.unspecified"), (309, 311)),(UniCase::ascii("vnd.dece.zip"), (311, 313)),(UniCase::ascii("vnd.denovo.fcselayout-link"), (313, 314)),(UniCase::ascii("vnd.dna"), (314, 315)),(UniCase::ascii("vnd.dolby.mlp"), (315, 316)),(UniCase::ascii("vnd.dpgraph"), (316, 317)),(UniCase::ascii("vnd.dreamfactory"), (317, 318)),(UniCase::ascii("vnd.ds-keypoint"), (318, 319)),(UniCase::ascii("vnd.dvb.ait"), (319, 320)),(UniCase::ascii("vnd.dynageo"), (320, 321)),(UniCase::ascii("vnd.ecowin.chart"), (321, 322)),(UniCase::ascii("vnd.enliven"), (322, 323)),(UniCase::ascii("vnd.epson.esf"), (323, 324)),(UniCase::ascii("vnd.epson.msf"), (324, 325)),(UniCase::ascii("vnd.epson.quickanime"), (325, 326)),(UniCase::ascii("vnd.epson.salt"), (326, 327)),(UniCase::ascii("vnd.epson.ssf"), (327, 328)),(UniCase::ascii("vnd.eszigno3+xml"), (328, 330)),(UniCase::ascii("vnd.ezpix-album"), (330, 331)),(UniCase::ascii("vnd.ezpix-package"), (331, 332)),(UniCase::ascii("vnd.fdf"), (332, 333)),(UniCase::ascii("vnd.fdsn.mseed"), (333, 334)),(UniCase::ascii("vnd.fdsn.seed"), (334, 336)),(UniCase::ascii("vnd.flographit"), (336, 337)),(UniCase::ascii("vnd.fluxtime.clip"), (337, 338)),(UniCase::ascii("vnd.framemaker"), (338, 342)),(UniCase::ascii("vnd.frogans.fnc"), (342, 343)),(UniCase::ascii("vnd.frogans.ltf"), (343, 344)),(UniCase::ascii("vnd.fsc.weblaunch"), (344, 345)),(UniCase::ascii("vnd.fujitsu.oasys"), (345, 346)),(UniCase::ascii("vnd.fujitsu.oasys2"), (346, 347)),(UniCase::ascii("vnd.fujitsu.oasys3"), (347, 348)),(UniCase::ascii("vnd.fujitsu.oasysgp"), (348, 349)),(UniCase::ascii("vnd.fujitsu.oasysprs"), (349, 350)),(UniCase::ascii("vnd.fujixerox.ddd"), (350, 351)),(UniCase::ascii("vnd.fujixerox.docuworks"), (351, 352)),(UniCase::ascii("vnd.fujixerox.docuworks.binder"), (352, 353)),(UniCase::ascii("vnd.fuzzysheet"), (353, 354)),(UniCase::ascii("vnd.genomatix.tuxedo"), (354, 355)),(UniCase::ascii("vnd.geogebra.file"), (355, 356)),(UniCase::ascii("vnd.geogebra.tool"), (356, 357)),(UniCase::ascii("vnd.geometry-explorer"), (357, 359)),(UniCase::ascii("vnd.geonext"), (359, 360)),(UniCase::ascii("vnd.geoplan"), (360, 361)),(UniCase::ascii("vnd.geospace"), (361, 362)),(UniCase::ascii("vnd.gmx"), (362, 363)),(UniCase::ascii("vnd.google-apps.document"), (363, 364)),(UniCase::ascii("vnd.google-apps.presentation"), (364, 365)),(UniCase::ascii("vnd.google-apps.spreadsheet"), (365, 366)),(UniCase::ascii("vnd.google-earth.kml+xml"), (366, 367)),(UniCase::ascii("vnd.google-earth.kmz"), (367, 368)),(UniCase::ascii("vnd.grafeq"), (368, 370)),(UniCase::ascii("vnd.groove-account"), (370, 371)),(UniCase::ascii("vnd.groove-help"), (371, 372)),(UniCase::ascii("vnd.groove-identity-message"), (372, 373)),(UniCase::ascii("vnd.groove-injector"), (373, 374)),(UniCase::ascii("vnd.groove-tool-message"), (374, 375)),(UniCase::ascii("vnd.groove-tool-template"), (375, 376)),(UniCase::ascii("vnd.groove-vcard"), (376, 377)),(UniCase::ascii("vnd.hal+xml"), (377, 378)),(UniCase::ascii("vnd.handheld-entertainment+xml"), (378, 379)),(UniCase::ascii("vnd.hbci"), (379, 380)),(UniCase::ascii("vnd.hhe.lesson-player"), (380, 381)),(UniCase::ascii("vnd.hp-hpgl"), (381, 382)),(UniCase::ascii("vnd.hp-hpid"), (382, 383)),(UniCase::ascii("vnd.hp-hps"), (383, 384)),(UniCase::ascii("vnd.hp-jlyt"), (384, 385)),(UniCase::ascii("vnd.hp-pcl"), (385, 386)),(UniCase::ascii("vnd.hp-pclxl"), (386, 387)),(UniCase::ascii("vnd.hydrostatix.sof-data"), (387, 388)),(UniCase::ascii("vnd.ibm.minipay"), (388, 389)),(UniCase::ascii("vnd.ibm.modcap"), (389, 392)),(UniCase::ascii("vnd.ibm.rights-management"), (392, 393)),(UniCase::ascii("vnd.ibm.secure-container"), (393, 394)),(UniCase::ascii("vnd.iccprofile"), (394, 396)),(UniCase::ascii("vnd.igloader"), (396, 397)),(UniCase::ascii("vnd.immervision-ivp"), (397, 398)),(UniCase::ascii("vnd.immervision-ivu"), (398, 399)),(UniCase::ascii("vnd.insors.igm"), (399, 400)),(UniCase::ascii("vnd.intercon.formnet"), (400, 402)),(UniCase::ascii("vnd.intergeo"), (402, 403)),(UniCase::ascii("vnd.intu.qbo"), (403, 404)),(UniCase::ascii("vnd.intu.qfx"), (404, 405)),(UniCase::ascii("vnd.ipunplugged.rcprofile"), (405, 406)),(UniCase::ascii("vnd.irepository.package+xml"), (406, 407)),(UniCase::ascii("vnd.is-xpr"), (407, 408)),(UniCase::ascii("vnd.isac.fcs"), (408, 409)),(UniCase::ascii("vnd.jam"), (409, 410)),(UniCase::ascii("vnd.jcp.javame.midlet-rms"), (410, 411)),(UniCase::ascii("vnd.jisp"), (411, 412)),(UniCase::ascii("vnd.joost.joda-archive"), (412, 413)),(UniCase::ascii("vnd.kahootz"), (413, 415)),(UniCase::ascii("vnd.kde.karbon"), (415, 416)),(UniCase::ascii("vnd.kde.kchart"), (416, 417)),(UniCase::ascii("vnd.kde.kformula"), (417, 418)),(UniCase::ascii("vnd.kde.kivio"), (418, 419)),(UniCase::ascii("vnd.kde.kontour"), (419, 420)),(UniCase::ascii("vnd.kde.kpresenter"), (420, 422)),(UniCase::ascii("vnd.kde.kspread"), (422, 423)),(UniCase::ascii("vnd.kde.kword"), (423, 425)),(UniCase::ascii("vnd.kenameaapp"), (425, 426)),(UniCase::ascii("vnd.kidspiration"), (426, 427)),(UniCase::ascii("vnd.kinar"), (427, 429)),(UniCase::ascii("vnd.koan"), (429, 432)),(UniCase::ascii("vnd.kodak-descriptor"), (432, 433)),(UniCase::ascii("vnd.las.las+xml"), (433, 434)),(UniCase::ascii("vnd.llamagraphics.life-balance.desktop"), (434, 435)),(UniCase::ascii("vnd.llamagraphics.life-balance.exchange+xml"), (435, 436)),(UniCase::ascii("vnd.lotus-1-2-3"), (436, 437)),(UniCase::ascii("vnd.lotus-approach"), (437, 438)),(UniCase::ascii("vnd.lotus-freelance"), (438, 439)),(UniCase::ascii("vnd.lotus-notes"), (439, 440)),(UniCase::ascii("vnd.lotus-organizer"), (440, 441)),(UniCase::ascii("vnd.lotus-screencam"), (441, 442)),(UniCase::ascii("vnd.lotus-wordpro"), (442, 443)),(UniCase::ascii("vnd.macports.portpkg"), (443, 444)),(UniCase::ascii("vnd.mcd"), (444, 445)),(UniCase::ascii("vnd.medcalcdata"), (445, 446)),(UniCase::ascii("vnd.mediastation.cdkey"), (446, 447)),(UniCase::ascii("vnd.mfer"), (447, 448)),(UniCase::ascii("vnd.mfmp"), (448, 449)),(UniCase::ascii("vnd.micrografx.flo"), (449, 450)),(UniCase::ascii("vnd.micrografx.igx"), (450, 451)),(UniCase::ascii("vnd.mif"), (451, 452)),(UniCase::ascii("vnd.mobius.daf"), (452, 453)),(UniCase::ascii("vnd.mobius.dis"), (453, 454)),(UniCase::ascii("vnd.mobius.mbk"), (454, 455)),(UniCase::ascii("vnd.mobius.mqy"), (455, 456)),(UniCase::ascii("vnd.mobius.msl"), (456, 457)),(UniCase::ascii("vnd.mobius.plc"), (457, 458)),(UniCase::ascii("vnd.mobius.txf"), (458, 459)),(UniCase::ascii("vnd.mophun.application"), (459, 460)),(UniCase::ascii("vnd.mophun.certificate"), (460, 461)),(UniCase::ascii("vnd.mozilla.xul+xml"), (461, 462)),(UniCase::ascii("vnd.ms-artgalry"), (462, 463)),(UniCase::ascii("vnd.ms-excel"), (463, 473)),(UniCase::ascii("vnd.ms-excel.addin.macroEnabled.12"), (473, 474)),(UniCase::ascii("vnd.ms-excel.sheet.binary.macroEnabled.12"), (474, 475)),(UniCase::ascii("vnd.ms-excel.sheet.macroEnabled.12"), (475, 476)),(UniCase::ascii("vnd.ms-excel.template.macroEnabled.12"), (476, 477)),(UniCase::ascii("vnd.ms-fontobject"), (477, 478)),(UniCase::ascii("vnd.ms-htmlhelp"), (478, 479)),(UniCase::ascii("vnd.ms-ims"), (479, 480)),(UniCase::ascii("vnd.ms-lrm"), (480, 481)),(UniCase::ascii("vnd.ms-mediapackage"), (481, 482)),(UniCase::ascii("vnd.ms-office.calx"), (482, 483)),(UniCase::ascii("vnd.ms-officetheme"), (483, 484)),(UniCase::ascii("vnd.ms-outlook"), (484, 486)),(UniCase::ascii("vnd.ms-pki.certstore"), (486, 487)),(UniCase::ascii("vnd.ms-pki.pko"), (487, 488)),(UniCase::ascii("vnd.ms-pki.seccat"), (488, 489)),(UniCase::ascii("vnd.ms-pki.stl"), (489, 490)),(UniCase::ascii("vnd.ms-powerpoint"), (490, 495)),(UniCase::ascii("vnd.ms-powerpoint.addin.macroEnabled.12"), (495, 496)),(UniCase::ascii("vnd.ms-powerpoint.presentation.macroEnabled.12"), (496, 497)),(UniCase::ascii("vnd.ms-powerpoint.slide.macroEnabled.12"), (497, 498)),(UniCase::ascii("vnd.ms-powerpoint.slideshow.macroEnabled.12"), (498, 499)),(UniCase::ascii("vnd.ms-powerpoint.template.macroEnabled.12"), (499, 500)),(UniCase::ascii("vnd.ms-project"), (500, 502)),(UniCase::ascii("vnd.ms-visio.viewer"), (502, 503)),(UniCase::ascii("vnd.ms-word.document.macroEnabled.12"), (503, 504)),(UniCase::ascii("vnd.ms-word.template.macroEnabled.12"), (504, 505)),(UniCase::ascii("vnd.ms-works"), (505, 509)),(UniCase::ascii("vnd.ms-wpl"), (509, 510)),(UniCase::ascii("vnd.ms-xpsdocument"), (510, 511)),(UniCase::ascii("vnd.mseq"), (511, 512)),(UniCase::ascii("vnd.musician"), (512, 513)),(UniCase::ascii("vnd.muvee.style"), (513, 514)),(UniCase::ascii("vnd.mynfc"), (514, 515)),(UniCase::ascii("vnd.neurolanguage.nlu"), (515, 516)),(UniCase::ascii("vnd.nitf"), (516, 518)),(UniCase::ascii("vnd.noblenet-directory"), (518, 519)),(UniCase::ascii("vnd.noblenet-sealer"), (519, 520)),(UniCase::ascii("vnd.noblenet-web"), (520, 521)),(UniCase::ascii("vnd.nokia.n-gage.data"), (521, 522)),(UniCase::ascii("vnd.nokia.n-gage.symbian.install"), (522, 523)),(UniCase::ascii("vnd.nokia.radio-preset"), (523, 524)),(UniCase::ascii("vnd.nokia.radio-presets"), (524, 525)),(UniCase::ascii("vnd.novadigm.edm"), (525, 526)),(UniCase::ascii("vnd.novadigm.edx"), (526, 527)),(UniCase::ascii("vnd.novadigm.ext"), (527, 528)),(UniCase::ascii("vnd.oasis.opendocument.chart"), (528, 529)),(UniCase::ascii("vnd.oasis.opendocument.chart-template"), (529, 530)),(UniCase::ascii("vnd.oasis.opendocument.database"), (530, 531)),(UniCase::ascii("vnd.oasis.opendocument.formula"), (531, 532)),(UniCase::ascii("vnd.oasis.opendocument.formula-template"), (532, 533)),(UniCase::ascii("vnd.oasis.opendocument.graphics"), (533, 534)),(UniCase::ascii("vnd.oasis.opendocument.graphics-template"), (534, 535)),(UniCase::ascii("vnd.oasis.opendocument.image"), (535, 536)),(UniCase::ascii("vnd.oasis.opendocument.image-template"), (536, 537)),(UniCase::ascii("vnd.oasis.opendocument.presentation"), (537, 538)),(UniCase::ascii("vnd.oasis.opendocument.presentation-template"), (538, 539)),(UniCase::ascii("vnd.oasis.opendocument.spreadsheet"), (539, 540)),(UniCase::ascii("vnd.oasis.opendocument.spreadsheet-template"), (540, 541)),(UniCase::ascii("vnd.oasis.opendocument.text"), (541, 542)),(UniCase::ascii("vnd.oasis.opendocument.text-master"), (542, 543)),(UniCase::ascii("vnd.oasis.opendocument.text-template"), (543, 544)),(UniCase::ascii("vnd.oasis.opendocument.text-web"), (544, 545)),(UniCase::ascii("vnd.olpc-sugar"), (545, 546)),(UniCase::ascii("vnd.oma.dd2+xml"), (546, 547)),(UniCase::ascii("vnd.openofficeorg.extension"), (547, 548)),(UniCase::ascii("vnd.openxmlformats-officedocument.presentationml.presentation"), (548, 549)),(UniCase::ascii("vnd.openxmlformats-officedocument.presentationml.slide"), (549, 550)),(UniCase::ascii("vnd.openxmlformats-officedocument.presentationml.slideshow"), (550, 551)),(UniCase::ascii("vnd.openxmlformats-officedocument.presentationml.template"), (551, 552)),(UniCase::ascii("vnd.openxmlformats-officedocument.spreadsheetml.sheet"), (552, 553)),(UniCase::ascii("vnd.openxmlformats-officedocument.spreadsheetml.template"), (553, 554)),(UniCase::ascii("vnd.openxmlformats-officedocument.wordprocessingml.document"), (554, 555)),(UniCase::ascii("vnd.openxmlformats-officedocument.wordprocessingml.template"), (555, 556)),(UniCase::ascii("vnd.osgeo.mapguide.package"), (556, 557)),(UniCase::ascii("vnd.osgi.dp"), (557, 558)),(UniCase::ascii("vnd.osgi.subsystem"), (558, 559)),(UniCase::ascii("vnd.palm"), (559, 562)),(UniCase::ascii("vnd.pawaafile"), (562, 563)),(UniCase::ascii("vnd.pg.format"), (563, 564)),(UniCase::ascii("vnd.pg.osasli"), (564, 565)),(UniCase::ascii("vnd.picsel"), (565, 566)),(UniCase::ascii("vnd.pmi.widget"), (566, 567)),(UniCase::ascii("vnd.pocketlearn"), (567, 568)),(UniCase::ascii("vnd.powerbuilder6"), (568, 569)),(UniCase::ascii("vnd.previewsystems.box"), (569, 570)),(UniCase::ascii("vnd.proteus.magazine"), (570, 571)),(UniCase::ascii("vnd.publishare-delta-tree"), (571, 572)),(UniCase::ascii("vnd.pvi.ptid1"), (572, 573)),(UniCase::ascii("vnd.quark.quarkxpress"), (573, 578)),(UniCase::ascii("vnd.realvnc.bed"), (578, 579)),(UniCase::ascii("vnd.recordare.musicxml"), (579, 580)),(UniCase::ascii("vnd.recordare.musicxml+xml"), (580, 581)),(UniCase::ascii("vnd.rig.cryptonote"), (581, 582)),(UniCase::ascii("vnd.rn-realmedia"), (582, 583)),(UniCase::ascii("vnd.rn-realmedia-vbr"), (583, 584)),(UniCase::ascii("vnd.rn-rn_music_package"), (584, 585)),(UniCase::ascii("vnd.route66.link66+xml"), (585, 586)),(UniCase::ascii("vnd.sailingtracker.track"), (586, 587)),(UniCase::ascii("vnd.seemail"), (587, 588)),(UniCase::ascii("vnd.sema"), (588, 589)),(UniCase::ascii("vnd.semd"), (589, 590)),(UniCase::ascii("vnd.semf"), (590, 591)),(UniCase::ascii("vnd.shana.informed.formdata"), (591, 592)),(UniCase::ascii("vnd.shana.informed.formtemplate"), (592, 593)),(UniCase::ascii("vnd.shana.informed.interchange"), (593, 594)),(UniCase::ascii("vnd.shana.informed.package"), (594, 595)),(UniCase::ascii("vnd.simtech-mindmapper"), (595, 597)),(UniCase::ascii("vnd.smart.teacher"), (597, 598)),(UniCase::ascii("vnd.solent.sdkm+xml"), (598, 600)),(UniCase::ascii("vnd.spotfire.dxp"), (600, 601)),(UniCase::ascii("vnd.spotfire.sfs"), (601, 602)),(UniCase::ascii("vnd.stardivision.calc"), (602, 603)),(UniCase::ascii("vnd.stardivision.draw"), (603, 604)),(UniCase::ascii("vnd.stardivision.impress"), (604, 605)),(UniCase::ascii("vnd.stardivision.math"), (605, 606)),(UniCase::ascii("vnd.stardivision.writer"), (606, 608)),(UniCase::ascii("vnd.stardivision.writer-global"), (608, 609)),(UniCase::ascii("vnd.stepmania.package"), (609, 610)),(UniCase::ascii("vnd.stepmania.stepchart"), (610, 611)),(UniCase::ascii("vnd.sun.wadl+xml"), (611, 612)),(UniCase::ascii("vnd.sun.xml.calc"), (612, 613)),(UniCase::ascii("vnd.sun.xml.calc.template"), (613, 614)),(UniCase::ascii("vnd.sun.xml.draw"), (614, 615)),(UniCase::ascii("vnd.sun.xml.draw.template"), (615, 616)),(UniCase::ascii("vnd.sun.xml.impress"), (616, 617)),(UniCase::ascii("vnd.sun.xml.impress.template"), (617, 618)),(UniCase::ascii("vnd.sun.xml.math"), (618, 619)),(UniCase::ascii("vnd.sun.xml.writer"), (619, 620)),(UniCase::ascii("vnd.sun.xml.writer.global"), (620, 621)),(UniCase::ascii("vnd.sun.xml.writer.template"), (621, 622)),(UniCase::ascii("vnd.sus-calendar"), (622, 624)),(UniCase::ascii("vnd.svd"), (624, 625)),(UniCase::ascii("vnd.symbian.install"), (625, 627)),(UniCase::ascii("vnd.syncml+xml"), (627, 628)),(UniCase::ascii("vnd.syncml.dm+wbxml"), (628, 629)),(UniCase::ascii("vnd.syncml.dm+xml"), (629, 630)),(UniCase::ascii("vnd.tao.intent-module-archive"), (630, 631)),(UniCase::ascii("vnd.tcpdump.pcap"), (631, 634)),(UniCase::ascii("vnd.tmobile-livetv"), (634, 635)),(UniCase::ascii("vnd.trid.tpt"), (635, 636)),(UniCase::ascii("vnd.triscape.mxs"), (636, 637)),(UniCase::ascii("vnd.trueapp"), (637, 638)),(UniCase::ascii("vnd.ufdl"), (638, 640)),(UniCase::ascii("vnd.uiq.theme"), (640, 641)),(UniCase::ascii("vnd.umajin"), (641, 642)),(UniCase::ascii("vnd.unity"), (642, 643)),(UniCase::ascii("vnd.uoml+xml"), (643, 644)),(UniCase::ascii("vnd.vcx"), (644, 645)),(UniCase::ascii("vnd.visio"), (645, 651)),(UniCase::ascii("vnd.visionary"), (651, 652)),(UniCase::ascii("vnd.vsf"), (652, 653)),(UniCase::ascii("vnd.wap.wbxml"), (653, 654)),(UniCase::ascii("vnd.wap.wmlc"), (654, 655)),(UniCase::ascii("vnd.wap.wmlscriptc"), (655, 656)),(UniCase::ascii("vnd.webturbo"), (656, 657)),(UniCase::ascii("vnd.wolfram.player"), (657, 658)),(UniCase::ascii("vnd.wordperfect"), (658, 659)),(UniCase::ascii("vnd.wqd"), (659, 660)),(UniCase::ascii("vnd.wt.stf"), (660, 661)),(UniCase::ascii("vnd.xara"), (661, 662)),(UniCase::ascii("vnd.xfdl"), (662, 663)),(UniCase::ascii("vnd.yamaha.hv-dic"), (663, 664)),(UniCase::ascii("vnd.yamaha.hv-script"), (664, 665)),(UniCase::ascii("vnd.yamaha.hv-voice"), (665, 666)),(UniCase::ascii("vnd.yamaha.openscoreformat"), (666, 667)),(UniCase::ascii("vnd.yamaha.openscoreformat.osfpvg+xml"), (667, 668)),(UniCase::ascii("vnd.yamaha.smaf-audio"), (668, 669)),(UniCase::ascii("vnd.yamaha.smaf-phrase"), (669, 670)),(UniCase::ascii("vnd.yellowriver-custom-menu"), (670, 671)),(UniCase::ascii("vnd.zul"), (671, 673)),(UniCase::ascii("vnd.zzazz.deck+xml"), (673, 674)),(UniCase::ascii("voicexml+xml"), (674, 675)),(UniCase::ascii("vsix"), (675, 676)),(UniCase::ascii("wasm"), (676, 677)),(UniCase::ascii("widget"), (677, 678)),(UniCase::ascii("windows-library+xml"), (678, 679)),(UniCase::ascii("windows-search-connector+xml"), (679, 680)),(UniCase::ascii("winhlp"), (680, 681)),(UniCase::ascii("wlmoviemaker"), (681, 682)),(UniCase::ascii("wspolicy+xml"), (682, 683)),(UniCase::ascii("x-7z-compressed"), (683, 684)),(UniCase::ascii("x-abiword"), (684, 685)),(UniCase::ascii("x-ace-compressed"), (685, 686)),(UniCase::ascii("x-arj"), (686, 687)),(UniCase::ascii("x-authorware-bin"), (687, 690)),(UniCase::ascii("x-authorware-map"), (690, 691)),(UniCase::ascii("x-authorware-seg"), (691, 692)),(UniCase::ascii("x-bcpio"), (692, 693)),(UniCase::ascii("x-bittorrent"), (693, 694)),(UniCase::ascii("x-blorb"), (694, 696)),(UniCase::ascii("x-bridge-url"), (696, 697)),(UniCase::ascii("x-bzip"), (697, 698)),(UniCase::ascii("x-bzip2"), (698, 700)),(UniCase::ascii("x-cbr"), (700, 705)),(UniCase::ascii("x-cdf"), (705, 706)),(UniCase::ascii("x-cdlink"), (706, 707)),(UniCase::ascii("x-cfs-compressed"), (707, 708)),(UniCase::ascii("x-chat"), (708, 709)),(UniCase::ascii("x-chess-pgn"), (709, 710)),(UniCase::ascii("x-chrome-extension"), (710, 711)),(UniCase::ascii("x-cocoa"), (711, 712)),(UniCase::ascii("x-compress"), (712, 713)),(UniCase::ascii("x-compressed"), (713, 714)),(UniCase::ascii("x-cpio"), (714, 715)),(UniCase::ascii("x-csh"), (715, 716)),(UniCase::ascii("x-debian-package"), (716, 717)),(UniCase::ascii("x-dgc-compressed"), (717, 718)),(UniCase::ascii("x-director"), (718, 727)),(UniCase::ascii("x-doom"), (727, 728)),(UniCase::ascii("x-dtbncx+xml"), (728, 729)),(UniCase::ascii("x-dtbook+xml"), (729, 730)),(UniCase::ascii("x-dtbresource+xml"), (730, 731)),(UniCase::ascii("x-dvi"), (731, 732)),(UniCase::ascii("x-dxf"), (732, 733)),(UniCase::ascii("x-endace-erf"), (733, 734)),(UniCase::ascii("x-eva"), (734, 735)),(UniCase::ascii("x-font-bdf"), (735, 736)),(UniCase::ascii("x-font-ghostscript"), (736, 737)),(UniCase::ascii("x-font-linux-psf"), (737, 738)),(UniCase::ascii("x-font-pcf"), (738, 739)),(UniCase::ascii("x-font-snf"), (739, 740)),(UniCase::ascii("x-font-ttf"), (740, 741)),(UniCase::ascii("x-font-type1"), (741, 742)),(UniCase::ascii("x-freearc"), (742, 743)),(UniCase::ascii("x-gca-compressed"), (743, 744)),(UniCase::ascii("x-glulx"), (744, 745)),(UniCase::ascii("x-gnumeric"), (745, 746)),(UniCase::ascii("x-gramps-xml"), (746, 747)),(UniCase::ascii("x-gtar"), (747, 748)),(UniCase::ascii("x-gzip"), (748, 749)),(UniCase::ascii("x-hdf"), (749, 750)),(UniCase::ascii("x-httpd-php"), (750, 751)),(UniCase::ascii("x-install-instructions"), (751, 752)),(UniCase::ascii("x-internet-signup"), (752, 754)),(UniCase::ascii("x-iphone"), (754, 755)),(UniCase::ascii("x-itunes-ipa"), (755, 756)),(UniCase::ascii("x-itunes-ipg"), (756, 757)),(UniCase::ascii("x-itunes-ipsw"), (757, 758)),(UniCase::ascii("x-itunes-ite"), (758, 759)),(UniCase::ascii("x-itunes-itlp"), (759, 760)),(UniCase::ascii("x-itunes-itms"), (760, 761)),(UniCase::ascii("x-itunes-itpc"), (761, 762)),(UniCase::ascii("x-java-applet"), (762, 763)),(UniCase::ascii("x-java-archive-diff"), (763, 764)),(UniCase::ascii("x-java-jnlp-file"), (764, 765)),(UniCase::ascii("x-koan"), (765, 766)),(UniCase::ascii("x-latex"), (766, 767)),(UniCase::ascii("x-lua-bytecode"), (767, 768)),(UniCase::ascii("x-lzh-compressed"), (768, 769)),(UniCase::ascii("x-makeself"), (769, 770)),(UniCase::ascii("x-mie"), (770, 771)),(UniCase::ascii("x-miva-compiled"), (771, 772)),(UniCase::ascii("x-mmxp"), (772, 773)),(UniCase::ascii("x-mobipocket-ebook"), (773, 775)),(UniCase::ascii("x-ms-application"), (775, 776)),(UniCase::ascii("x-ms-license"), (776, 777)),(UniCase::ascii("x-ms-manifest"), (777, 778)),(UniCase::ascii("x-ms-reader"), (778, 779)),(UniCase::ascii("x-ms-shortcut"), (779, 780)),(UniCase::ascii("x-ms-vsto"), (780, 781)),(UniCase::ascii("x-ms-wmd"), (781, 782)),(UniCase::ascii("x-ms-wmz"), (782, 783)),(UniCase::ascii("x-ms-xbap"), (783, 784)),(UniCase::ascii("x-msaccess"), (784, 785)),(UniCase::ascii("x-msbinder"), (785, 786)),(UniCase::ascii("x-mscardfile"), (786, 787)),(UniCase::ascii("x-msclip"), (787, 788)),(UniCase::ascii("x-msdownload"), (788, 791)),(UniCase::ascii("x-msmediaview"), (791, 794)),(UniCase::ascii("x-msmetafile"), (794, 796)),(UniCase::ascii("x-msmoney"), (796, 797)),(UniCase::ascii("x-mspublisher"), (797, 798)),(UniCase::ascii("x-msschedule"), (798, 799)),(UniCase::ascii("x-msterminal"), (799, 800)),(UniCase::ascii("x-mswrite"), (800, 801)),(UniCase::ascii("x-netcdf"), (801, 802)),(UniCase::ascii("x-ns-proxy-autoconfig"), (802, 803)),(UniCase::ascii("x-nzb"), (803, 804)),(UniCase::ascii("x-oleobject"), (804, 805)),(UniCase::ascii("x-parquet"), (805, 806)),(UniCase::ascii("x-perfmon"), (806, 811)),(UniCase::ascii("x-perl"), (811, 813)),(UniCase::ascii("x-pkcs12"), (813, 815)),(UniCase::ascii("x-pkcs7-certificates"), (815, 817)),(UniCase::ascii("x-pkcs7-certreqresp"), (817, 818)),(UniCase::ascii("x-podcast"), (818, 819)),(UniCase::ascii("x-quicktimeplayer"), (819, 820)),(UniCase::ascii("x-rar-compressed"), (820, 821)),(UniCase::ascii("x-research-info-systems"), (821, 822)),(UniCase::ascii("x-safari-safariextz"), (822, 823)),(UniCase::ascii("x-safari-webarchive"), (823, 824)),(UniCase::ascii("x-sgimb"), (824, 825)),(UniCase::ascii("x-sh"), (825, 826)),(UniCase::ascii("x-shar"), (826, 827)),(UniCase::ascii("x-shockwave-flash"), (827, 829)),(UniCase::ascii("x-silverlight-app"), (829, 830)),(UniCase::ascii("x-smaf"), (830, 831)),(UniCase::ascii("x-sql"), (831, 832)),(UniCase::ascii("x-stuffit"), (832, 833)),(UniCase::ascii("x-stuffitx"), (833, 834)),(UniCase::ascii("x-subrip"), (834, 835)),(UniCase::ascii("x-sv4cpio"), (835, 836)),(UniCase::ascii("x-sv4crc"), (836, 837)),(UniCase::ascii("x-t3vm-image"), (837, 838)),(UniCase::ascii("x-tads"), (838, 839)),(UniCase::ascii("x-tar"), (839, 840)),(UniCase::ascii("x-tcl"), (840, 842)),(UniCase::ascii("x-tex"), (842, 843)),(UniCase::ascii("x-tex-tfm"), (843, 844)),(UniCase::ascii("x-texinfo"), (844, 846)),(UniCase::ascii("x-tgif"), (846, 847)),(UniCase::ascii("x-troff"), (847, 850)),(UniCase::ascii("x-troff-man"), (850, 851)),(UniCase::ascii("x-troff-me"), (851, 852)),(UniCase::ascii("x-troff-ms"), (852, 853)),(UniCase::ascii("x-ustar"), (853, 854)),(UniCase::ascii("x-virtualbox-hdd"), (854, 855)),(UniCase::ascii("x-virtualbox-ova"), (855, 856)),(UniCase::ascii("x-virtualbox-ovf"), (856, 857)),(UniCase::ascii("x-virtualbox-vbox"), (857, 858)),(UniCase::ascii("x-virtualbox-vbox-extpack"), (858, 859)),(UniCase::ascii("x-virtualbox-vdi"), (859, 860)),(UniCase::ascii("x-virtualbox-vhd"), (860, 861)),(UniCase::ascii("x-virtualbox-vmdk"), (861, 862)),(UniCase::ascii("x-wais-source"), (862, 863)),(UniCase::ascii("x-web-app-manifest+json"), (863, 864)),(UniCase::ascii("x-wlpg-detect"), (864, 865)),(UniCase::ascii("x-wlpg3-detect"), (865, 866)),(UniCase::ascii("x-x509-ca-cert"), (866, 870)),(UniCase::ascii("x-xfig"), (870, 871)),(UniCase::ascii("x-xliff+xml"), (871, 872)),(UniCase::ascii("x-xpinstall"), (872, 873)),(UniCase::ascii("x-xz"), (873, 874)),(UniCase::ascii("x-zmachine"), (874, 882)),(UniCase::ascii("xaml+xml"), (882, 883)),(UniCase::ascii("xcap-diff+xml"), (883, 884)),(UniCase::ascii("xenc+xml"), (884, 885)),(UniCase::ascii("xhtml+xml"), (885, 887)),(UniCase::ascii("xml"), (887, 931)),(UniCase::ascii("xop+xml"), (931, 932)),(UniCase::ascii("xproc+xml"), (932, 933)),(UniCase::ascii("xspf+xml"), (933, 934)),(UniCase::ascii("xv+xml"), (934, 938)),(UniCase::ascii("yang"), (938, 939)),(UniCase::ascii("yin+xml"), (939, 940)),(UniCase::ascii("zip"), (940, 941)),] }),(UniCase::ascii("audio"), TopLevelExts { start: 941, end: 1007, subs: &[(UniCase::ascii("aac"), (941, 943)),(UniCase::ascii("ac3"), (943, 944)),(UniCase::ascii("aiff"), (944, 948)),(UniCase::ascii("annodex"), (948, 949)),(UniCase::ascii("audible"), (949, 950)),(UniCase::ascii("basic"), (950, 952)),(UniCase::ascii("flac"), (952, 953)),(UniCase::ascii("m4a"), (953, 954)),(UniCase::ascii("m4b"), (954, 955)),(UniCase::ascii("m4p"), (955, 956)),(UniCase::ascii("mid"), (956, 959)),(UniCase::ascii("midi"), (959, 960)),(UniCase::ascii("mp4"), (960, 961)),(UniCase::ascii("mpeg"), (961, 967)),(UniCase::ascii("ogg"), (967, 971)),(UniCase::ascii("s3m"), (971, 972)),(UniCase::ascii("scpls"), (972, 973)),(UniCase::ascii("silk"), (973, 974)),(UniCase::ascii("vnd.audible.aax"), (974, 975)),(UniCase::ascii("vnd.dece.audio"), (975, 977)),(UniCase::ascii("vnd.digital-winds"), (977, 978)),(UniCase::ascii("vnd.dlna.adts"), (978, 979)),(UniCase::ascii("vnd.dra"), (979, 980)),(UniCase::ascii("vnd.dts"), (980, 981)),(UniCase::ascii("vnd.dts.hd"), (981, 982)),(UniCase::ascii("vnd.lucent.voice"), (982, 983)),(UniCase::ascii("vnd.ms-playready.media.pya"), (983, 984)),(UniCase::ascii("vnd.nuera.ecelp4800"), (984, 985)),(UniCase::ascii("vnd.nuera.ecelp7470"), (985, 986)),(UniCase::ascii("vnd.nuera.ecelp9600"), (986, 987)),(UniCase::ascii("vnd.rip"), (987, 988)),(UniCase::ascii("wav"), (988, 990)),(UniCase::ascii("webm"), (990, 991)),(UniCase::ascii("x-caf"), (991, 992)),(UniCase::ascii("x-gsm"), (992, 993)),(UniCase::ascii("x-m4r"), (993, 994)),(UniCase::ascii("x-matroska"), (994, 995)),(UniCase::ascii("x-mpegurl"), (995, 997)),(UniCase::ascii("x-ms-wax"), (997, 998)),(UniCase::ascii("x-ms-wma"), (998, 999)),(UniCase::ascii("x-pn-realaudio"), (999, 1001)),(UniCase::ascii("x-pn-realaudio-plugin"), (1001, 1002)),(UniCase::ascii("x-sd2"), (1002, 1003)),(UniCase::ascii("x-smd"), (1003, 1006)),(UniCase::ascii("xm"), (1006, 1007)),] }),(UniCase::ascii("chemical"), TopLevelExts { start: 1007, end: 1013, subs: &[(UniCase::ascii("x-cdx"), (1007, 1008)),(UniCase::ascii("x-cif"), (1008, 1009)),(UniCase::ascii("x-cmdf"), (1009, 1010)),(UniCase::ascii("x-cml"), (1010, 1011)),(UniCase::ascii("x-csml"), (1011, 1012)),(UniCase::ascii("x-xyz"), (1012, 1013)),] }),(UniCase::ascii("drawing"), TopLevelExts { start: 1013, end: 1014, subs: &[(UniCase::ascii("x-dwf"), (1013, 1014)),] }),(UniCase::ascii("font"), TopLevelExts { start: 1014, end: 1017, subs: &[(UniCase::ascii("collection"), (1014, 1015)),(UniCase::ascii("ttf"), (1015, 1016)),(UniCase::ascii("woff2"), (1016, 1017)),] }),(UniCase::ascii("image"), TopLevelExts { start: 1017, end: 1119, subs: &[(UniCase::ascii("apng"), (1017, 1018)),(UniCase::ascii("avif"), (1018, 1019)),(UniCase::ascii("avif-sequence"), (1019, 1020)),(UniCase::ascii("bmp"), (1020, 1022)),(UniCase::ascii("cgm"), (1022, 1023)),(UniCase::ascii("cis-cod"), (1023, 1024)),(UniCase::ascii("g3fax"), (1024, 1025)),(UniCase::ascii("gif"), (1025, 1026)),(UniCase::ascii("heic"), (1026, 1027)),(UniCase::ascii("heic-sequence"), (1027, 1028)),(UniCase::ascii("heif"), (1028, 1029)),(UniCase::ascii("heif-sequence"), (1029, 1030)),(UniCase::ascii("ief"), (1030, 1031)),(UniCase::ascii("jp2"), (1031, 1033)),(UniCase::ascii("jpeg"), (1033, 1037)),(UniCase::ascii("jpm"), (1037, 1038)),(UniCase::ascii("jpx"), (1038, 1040)),(UniCase::ascii("jxl"), (1040, 1041)),(UniCase::ascii("ktx"), (1041, 1042)),(UniCase::ascii("pict"), (1042, 1045)),(UniCase::ascii("png"), (1045, 1047)),(UniCase::ascii("prs.btif"), (1047, 1048)),(UniCase::ascii("sgi"), (1048, 1049)),(UniCase::ascii("svg+xml"), (1049, 1051)),(UniCase::ascii("tiff"), (1051, 1053)),(UniCase::ascii("vnd.dece.graphic"), (1053, 1057)),(UniCase::ascii("vnd.djvu"), (1057, 1059)),(UniCase::ascii("vnd.fastbidsheet"), (1059, 1060)),(UniCase::ascii("vnd.fpx"), (1060, 1061)),(UniCase::ascii("vnd.fst"), (1061, 1062)),(UniCase::ascii("vnd.fujixerox.edmics-mmr"), (1062, 1063)),(UniCase::ascii("vnd.fujixerox.edmics-rlc"), (1063, 1064)),(UniCase::ascii("vnd.ms-modi"), (1064, 1065)),(UniCase::ascii("vnd.ms-photo"), (1065, 1066)),(UniCase::ascii("vnd.net-fpx"), (1066, 1067)),(UniCase::ascii("vnd.radiance"), (1067, 1068)),(UniCase::ascii("vnd.rn-realflash"), (1068, 1069)),(UniCase::ascii("vnd.wap.wbmp"), (1069, 1070)),(UniCase::ascii("vnd.xiff"), (1070, 1071)),(UniCase::ascii("webp"), (1071, 1072)),(UniCase::ascii("x-3ds"), (1072, 1073)),(UniCase::ascii("x-adobe-dng"), (1073, 1074)),(UniCase::ascii("x-canon-cr2"), (1074, 1075)),(UniCase::ascii("x-canon-cr3"), (1075, 1076)),(UniCase::ascii("x-canon-crw"), (1076, 1077)),(UniCase::ascii("x-cmu-raster"), (1077, 1078)),(UniCase::ascii("x-cmx"), (1078, 1079)),(UniCase::ascii("x-epson-erf"), (1079, 1080)),(UniCase::ascii("x-freehand"), (1080, 1085)),(UniCase::ascii("x-fuji-raf"), (1085, 1086)),(UniCase::ascii("x-icon"), (1086, 1087)),(UniCase::ascii("x-jg"), (1087, 1088)),(UniCase::ascii("x-jng"), (1088, 1089)),(UniCase::ascii("x-kodak-dcr"), (1089, 1090)),(UniCase::ascii("x-kodak-k25"), (1090, 1091)),(UniCase::ascii("x-kodak-kdc"), (1091, 1092)),(UniCase::ascii("x-macpaint"), (1092, 1095)),(UniCase::ascii("x-minolta-mrw"), (1095, 1096)),(UniCase::ascii("x-mrsid-image"), (1096, 1097)),(UniCase::ascii("x-nikon-nef"), (1097, 1098)),(UniCase::ascii("x-nikon-nrw"), (1098, 1099)),(UniCase::ascii("x-olympus-orf"), (1099, 1100)),(UniCase::ascii("x-panasonic-rw"), (1100, 1101)),(UniCase::ascii("x-panasonic-rw2"), (1101, 1103)),(UniCase::ascii("x-pentax-pef"), (1103, 1104)),(UniCase::ascii("x-portable-anymap"), (1104, 1105)),(UniCase::ascii("x-portable-bitmap"), (1105, 1106)),(UniCase::ascii("x-portable-graymap"), (1106, 1107)),(UniCase::ascii("x-portable-pixmap"), (1107, 1108)),(UniCase::ascii("x-quicktime"), (1108, 1110)),(UniCase::ascii("x-rgb"), (1110, 1111)),(UniCase::ascii("x-sigma-x3f"), (1111, 1112)),(UniCase::ascii("x-sony-arw"), (1112, 1113)),(UniCase::ascii("x-sony-sr2"), (1113, 1114)),(UniCase::ascii("x-sony-srf"), (1114, 1115)),(UniCase::ascii("x-tga"), (1115, 1116)),(UniCase::ascii("x-xbitmap"), (1116, 1117)),(UniCase::ascii("x-xpixmap"), (1117, 1118)),(UniCase::ascii("x-xwindowdump"), (1118, 1119)),] }),(UniCase::ascii("message"), TopLevelExts { start: 1119, end: 1129, subs: &[(UniCase::ascii("disposition-notification"), (1119, 1120)),(UniCase::ascii("global"), (1120, 1121)),(UniCase::ascii("global-delivery-status"), (1121, 1122)),(UniCase::ascii("global-disposition-notification"), (1122, 1123)),(UniCase::ascii("global-headers"), (1123, 1124)),(UniCase::ascii("rfc822"), (1124, 1129)),] }),(UniCase::ascii("model"), TopLevelExts { start: 1129, end: 1147, subs: &[(UniCase::ascii("gltf+json"), (1129, 1130)),(UniCase::ascii("gltf-binary"), (1130, 1131)),(UniCase::ascii("iges"), (1131, 1133)),(UniCase::ascii("mesh"), (1133, 1136)),(UniCase::ascii("vnd.collada+xml"), (1136, 1137)),(UniCase::ascii("vnd.gdl"), (1137, 1138)),(UniCase::ascii("vnd.gtw"), (1138, 1139)),(UniCase::ascii("vnd.vtu"), (1139, 1140)),(UniCase::ascii("vrml"), (1140, 1141)),(UniCase::ascii("x3d+binary"), (1141, 1143)),(UniCase::ascii("x3d+vrml"), (1143, 1145)),(UniCase::ascii("x3d+xml"), (1145, 1147)),] }),(UniCase::ascii("text"), TopLevelExts { start: 1147, end: 1330, subs: &[(UniCase::ascii("cache-manifest"), (1147, 1148)),(UniCase::ascii("calendar"), (1148, 1150)),(UniCase::ascii("coffeescript"), (1150, 1152)),(UniCase::ascii("css"), (1152, 1153)),(UniCase::ascii("csv"), (1153, 1154)),(UniCase::ascii("dlm"), (1154, 1155)),(UniCase::ascii("gemini"), (1155, 1157)),(UniCase::ascii("h323"), (1157, 1158)),(UniCase::ascii("html"), (1158, 1162)),(UniCase::ascii("iuls"), (1162, 1163)),(UniCase::ascii("jade"), (1163, 1164)),(UniCase::ascii("javascript"), (1164, 1169)),(UniCase::ascii("less"), (1169, 1170)),(UniCase::ascii("markdown"), (1170, 1172)),(UniCase::ascii("mathml"), (1172, 1173)),(UniCase::ascii("n3"), (1173, 1174)),(UniCase::ascii("plain"), (1174, 1242)),(UniCase::ascii("prs.lines.tag"), (1242, 1243)),(UniCase::ascii("richtext"), (1243, 1244)),(UniCase::ascii("scriptlet"), (1244, 1246)),(UniCase::ascii("sgml"), (1246, 1248)),(UniCase::ascii("shex"), (1248, 1249)),(UniCase::ascii("slim"), (1249, 1251)),(UniCase::ascii("stylus"), (1251, 1253)),(UniCase::ascii("tab-separated-values"), (1253, 1254)),(UniCase::ascii("turtle"), (1254, 1255)),(UniCase::ascii("uri-list"), (1255, 1258)),(UniCase::ascii("vbscript"), (1258, 1259)),(UniCase::ascii("vcard"), (1259, 1260)),(UniCase::ascii("vnd.curl"), (1260, 1261)),(UniCase::ascii("vnd.curl.dcurl"), (1261, 1262)),(UniCase::ascii("vnd.curl.mcurl"), (1262, 1263)),(UniCase::ascii("vnd.curl.scurl"), (1263, 1264)),(UniCase::ascii("vnd.dvb.subtitle"), (1264, 1265)),(UniCase::ascii("vnd.fly"), (1265, 1266)),(UniCase::ascii("vnd.fmi.flexstor"), (1266, 1267)),(UniCase::ascii("vnd.graphviz"), (1267, 1268)),(UniCase::ascii("vnd.in3d.3dml"), (1268, 1269)),(UniCase::ascii("vnd.in3d.spot"), (1269, 1270)),(UniCase::ascii("vnd.sun.j2me.app-descriptor"), (1270, 1271)),(UniCase::ascii("vnd.wap.wml"), (1271, 1272)),(UniCase::ascii("vnd.wap.wmlscript"), (1272, 1273)),(UniCase::ascii("vtt"), (1273, 1274)),(UniCase::ascii("webviewhtml"), (1274, 1275)),(UniCase::ascii("x-c"), (1275, 1276)),(UniCase::ascii("x-component"), (1276, 1277)),(UniCase::ascii("x-fortran"), (1277, 1281)),(UniCase::ascii("x-gherkin"), (1281, 1282)),(UniCase::ascii("x-handlebars-template"), (1282, 1283)),(UniCase::ascii("x-hdml"), (1283, 1284)),(UniCase::ascii("x-html-insertion"), (1284, 1286)),(UniCase::ascii("x-lua"), (1286, 1287)),(UniCase::ascii("x-markdown"), (1287, 1289)),(UniCase::ascii("x-ms-contact"), (1289, 1290)),(UniCase::ascii("x-ms-group"), (1290, 1291)),(UniCase::ascii("x-ms-iqy"), (1291, 1292)),(UniCase::ascii("x-ms-rqy"), (1292, 1293)),(UniCase::ascii("x-nfo"), (1293, 1294)),(UniCase::ascii("x-opml"), (1294, 1295)),(UniCase::ascii("x-pascal"), (1295, 1297)),(UniCase::ascii("x-processing"), (1297, 1298)),(UniCase::ascii("x-rust"), (1298, 1299)),(UniCase::ascii("x-sass"), (1299, 1300)),(UniCase::ascii("x-scss"), (1300, 1301)),(UniCase::ascii("x-setext"), (1301, 1302)),(UniCase::ascii("x-sfv"), (1302, 1303)),(UniCase::ascii("x-suse-ymp"), (1303, 1304)),(UniCase::ascii("x-toml"), (1304, 1305)),(UniCase::ascii("x-uuencode"), (1305, 1306)),(UniCase::ascii("x-vcard"), (1306, 1307)),(UniCase::ascii("x-yaml"), (1307, 1309)),(UniCase::ascii("xml"), (1309, 1330)),] }),(UniCase::ascii("video"), TopLevelExts { start: 1330, end: 1411, subs: &[(UniCase::ascii("3gpp"), (1330, 1332)),(UniCase::ascii("3gpp2"), (1332, 1334)),(UniCase::ascii("annodex"), (1334, 1335)),(UniCase::ascii("divx"), (1335, 1336)),(UniCase::ascii("h261"), (1336, 1337)),(UniCase::ascii("h263"), (1337, 1338)),(UniCase::ascii("h264"), (1338, 1339)),(UniCase::ascii("jpeg"), (1339, 1340)),(UniCase::ascii("jpm"), (1340, 1341)),(UniCase::ascii("mj2"), (1341, 1343)),(UniCase::ascii("mp4"), (1343, 1346)),(UniCase::ascii("mpeg"), (1346, 1357)),(UniCase::ascii("ogg"), (1357, 1358)),(UniCase::ascii("quicktime"), (1358, 1361)),(UniCase::ascii("vnd.dece.hd"), (1361, 1363)),(UniCase::ascii("vnd.dece.mobile"), (1363, 1365)),(UniCase::ascii("vnd.dece.pd"), (1365, 1367)),(UniCase::ascii("vnd.dece.sd"), (1367, 1369)),(UniCase::ascii("vnd.dece.video"), (1369, 1371)),(UniCase::ascii("vnd.dlna.mpeg-tts"), (1371, 1376)),(UniCase::ascii("vnd.dvb.file"), (1376, 1377)),(UniCase::ascii("vnd.fvt"), (1377, 1378)),(UniCase::ascii("vnd.mpegurl"), (1378, 1380)),(UniCase::ascii("vnd.ms-playready.media.pyv"), (1380, 1381)),(UniCase::ascii("vnd.uvvu.mp4"), (1381, 1383)),(UniCase::ascii("vnd.vivo"), (1383, 1384)),(UniCase::ascii("webm"), (1384, 1385)),(UniCase::ascii("x-dv"), (1385, 1387)),(UniCase::ascii("x-f4v"), (1387, 1388)),(UniCase::ascii("x-fli"), (1388, 1389)),(UniCase::ascii("x-flv"), (1389, 1390)),(UniCase::ascii("x-ivf"), (1390, 1391)),(UniCase::ascii("x-la-asf"), (1391, 1393)),(UniCase::ascii("x-m4v"), (1393, 1394)),(UniCase::ascii("x-matroska"), (1394, 1397)),(UniCase::ascii("x-mng"), (1397, 1398)),(UniCase::ascii("x-ms-asf"), (1398, 1402)),(UniCase::ascii("x-ms-vob"), (1402, 1403)),(UniCase::ascii("x-ms-wm"), (1403, 1404)),(UniCase::ascii("x-ms-wmp"), (1404, 1405)),(UniCase::ascii("x-ms-wmv"), (1405, 1406)),(UniCase::ascii("x-ms-wmx"), (1406, 1407)),(UniCase::ascii("x-ms-wvx"), (1407, 1408)),(UniCase::ascii("x-msvideo"), (1408, 1409)),(UniCase::ascii("x-sgi-movie"), (1409, 1410)),(UniCase::ascii("x-smv"), (1410, 1411)),] }),(UniCase::ascii("x-conference"), TopLevelExts { start: 1411, end: 1412, subs: &[(UniCase::ascii("x-cooltalk"), (1411, 1412)),] }),(UniCase::ascii("x-world"), TopLevelExts { start: 1412, end: 1417, subs: &[(UniCase::ascii("x-vrml"), (1412, 1417)),] }),];
const EXTS: &'static [&'static str] = &["dwg", "ez", "anx", "aw", "atom", "atomcat", "atomsvc", "bdoc", "ccxml", "cdmia", "cdmic", "cdmid", "cdmio", "cdmiq", "cu", "mpd", "davmount", "x", "dbk", "dssc", "xdssc", "emma", "evy", "epub", "etl", "exi", "otf", "ttf", "pfr", "woff", "fif", "fsscript", "fsx", "spl", "geojson", "gml", "gpx", "gxf", "gz", "hjson", "hta", "stk", "ink", "inkml", "acx", "ipfix", "ear", "jar", "war", "ser", "mjs", "json", "json5", "jsonml", "jsonld", "jck", "jcz", "lostxml", "hqx", "cpt", "mads", "webmanifest", "mrc", "mrcx", "ma", "mb", "nb", "mathml", "mbox", "mscml", "metalink", "meta4", "mets", "mods", "m21", "mp21", "mp4s", "amc", "vsi", "accdb", "accde", "accdt", "ade", "adp", "mda", "mde", "accda", "accdc", "accft", "accdr", "accdw", "doc", "dot", "wbk", "wiz", "mxf", "nq", "nt", "aaf", "aca", "afm", "asd", "asi", "bin", "bpk", "buffer", "cab", "cur", "dat", "deb", "deploy", "dist", "distz", "dmg", "dms", "dsp", "dump", "dwp", "elc", "emz", "exe", "fla", "hhk", "hhp", "hxd", "hxh", "hxi", "hxq", "hxr", "hxs", "hxw", "img", "inf", "iso", "java", "jpb", "lpk", "lrf", "lzh", "mar", "mdp", "mix", "msi", "msm", "mso", "msp", "ocx", "pcx", "pcz", "pfb", "pfm", "pkg", "prm", "prx", "psd", "psm", "psp", "qxd", "rvt", "sea", "smi", "snp", "so", "thn", "toc", "u32", "xmp", "xsn", "xtp", "oda", "opf", "ogx", "axs", "omdoc", "one", "onea", "onepkg", "onetmp", "onetoc", "onetoc2", "osdx", "oxps", "xer", "pdf", "pgp", "asc", "sig", "prf", "p10", "p7c", "p7m", "p7s", "p8", "ac", "crl", "pkipath", "pki", "ai", "eps", "ps", "psc1", "cww", "pskcxml", "raml", "rat", "rdf", "rif", "rnc", "rl", "rld", "gbr", "mft", "roa", "rsd", "rss", "rtf", "sbml", "scq", "scs", "spq", "spp", "sdp", "setpay", "setreg", "shf", "smil", "rq", "srx", "gram", "grxml", "sru", "ssdl", "ssml", "step", "stp", "ssm", "tei", "teicorpus", "tfi", "tsd", "trig", "plb", "psb", "pvb", "tcap", "pwn", "aso", "imp", "acu", "acutc", "atc", "air", "fcdt", "fxp", "fxpl", "xdp", "xfdf", "ahead", "azf", "azs", "azw", "acc", "ami", "apk", "cii", "fti", "atx", "parquet", "mpkg", "pkpass", "swi", "iota", "aep", "mpm", "bmi", "rep", "cdxml", "mmd", "cdy", "csl", "cla", "rp9", "c4d", "c4f", "c4g", "c4p", "c4u", "c11amc", "c11amz", "csp", "cdbcmsg", "cmc", "clkx", "clkk", "clkp", "clkt", "clkw", "wbs", "ppd", "car", "pcurl", "dart", "rdz", "uvd", "uvf", "uvvd", "uvvf", "uvt", "uvvt", "uvvx", "uvx", "uvvz", "uvz", "fe_launch", "dna", "mlp", "dpg", "dfac", "kpxx", "ait", "geo", "mag", "nml", "esf", "msf", "qam", "slt", "ssf", "es3", "et3", "ez2", "ez3", "fdf", "mseed", "dataless", "seed", "gph", "ftc", "book", "fm", "frame", "maker", "fnc", "ltf", "fsc", "oas", "oa2", "oa3", "fg5", "bh2", "ddd", "xdw", "xbd", "fzs", "txd", "ggb", "ggt", "gex", "gre", "gxt", "g2w", "g3w", "gmx", "gdoc", "gslides", "gsheet", "kml", "kmz", "gqf", "gqs", "gac", "ghf", "gim", "grv", "gtm", "tpl", "vcg", "hal", "zmm", "hbci", "les", "hpgl", "hpid", "hps", "jlt", "pcl", "pclxl", "sfd-hdstx", "mpy", "afp", "list3820", "listafp", "irm", "sc", "icc", "icm", "igl", "ivp", "ivu", "igm", "xpw", "xpx", "i2g", "qbo", "qfx", "rcprofile", "irp", "xpr", "fcs", "jam", "rms", "jisp", "joda", "ktr", "ktz", "karbon", "chrt", "kfo", "flw", "kon", "kpr", "kpt", "ksp", "kwd", "kwt", "htke", "kia", "kne", "knp", "skd", "skm", "skt", "sse", "lasxml", "lbd", "lbe", "123", "apr", "pre", "nsf", "org", "scm", "lwp", "portpkg", "mcd", "mc1", "cdkey", "mwf", "mfm", "flo", "igx", "mif", "daf", "dis", "mbk", "mqy", "msl", "plc", "txf", "mpn", "mpc", "xul", "cil", "slk", "xla", "xlc", "xld", "xlk", "xll", "xlm", "xls", "xlt", "xlw", "xlam", "xlsb", "xlsm", "xltm", "eot", "chm", "ims", "lrm", "mpf", "calx", "thmx", "msg", "pst", "sst", "pko", "cat", "stl", "pot", "ppa", "pps", "ppt", "pwz", "ppam", "pptm", "sldm", "ppsm", "potm", "mpp", "mpt", "vdx", "docm", "dotm", "wcm", "wdb", "wks", "wps", "wpl", "xps", "mseq", "mus", "msty", "taglet", "nlu", "nitf", "ntf", "nnd", "nns", "nnw", "ngdat", "n-gage", "rpst", "rpss", "edm", "edx", "ext", "odc", "otc", "odb", "odf", "odft", "odg", "otg", "odi", "oti", "odp", "otp", "ods", "ots", "odt", "odm", "ott", "oth", "xo", "dd2", "oxt", "pptx", "sldx", "ppsx", "potx", "xlsx", "xltx", "docx", "dotx", "mgp", "dp", "esa", "oprc", "pdb", "pqa", "paw", "str", "ei6", "efif", "wg", "plf", "pbd", "box", "mgz", "qps", "ptid", "qwd", "qwt", "qxb", "qxl", "qxt", "bed", "mxl", "musicxml", "cryptonote", "rm", "rmvb", "rmp", "link66", "st", "see", "sema", "semd", "semf", "ifm", "itp", "iif", "ipk", "twd", "twds", "teacher", "sdkd", "sdkm", "dxp", "sfs", "sdc", "sda", "sdd", "smf", "sdw", "vor", "sgl", "smzip", "sm", "wadl", "sxc", "stc", "sxd", "std", "sxi", "sti", "sxm", "sxw", "sxg", "stw", "sus", "susp", "svd", "sis", "sisx", "xsm", "bdm", "xdm", "tao", "cap", "dmp", "pcap", "tmo", "tpt", "mxs", "tra", "ufd", "ufdl", "utz", "umj", "unityweb", "uoml", "vcx", "vsd", "vss", "vst", "vsw", "vsx", "vtx", "vis", "vsf", "wbxml", "wmlc", "wmlsc", "wtb", "nbp", "wpd", "wqd", "stf", "xar", "xfdl", "hvd", "hvs", "hvp", "osf", "osfpvg", "saf", "spf", "cmp", "zir", "zirz", "zaz", "vxml", "vsix", "wasm", "wgt", "library-ms", "searchconnector-ms", "hlp", "wlmp", "wspolicy", "7z", "abw", "ace", "arj", "aab", "vox", "x32", "aam", "aas", "bcpio", "torrent", "blb", "blorb", "adobebridge", "bz", "boz", "bz2", "cb7", "cba", "cbr", "cbt", "cbz", "cdf", "vcd", "cfs", "chat", "pgn", "crx", "cco", "z", "tgz", "cpio", "csh", "udeb", "dgc", "cct", "cst", "cxt", "dcr", "dir", "dxr", "fgd", "swa", "w3d", "wad", "ncx", "dtb", "res", "dvi", "dxf", "erf", "eva", "bdf", "gsf", "psf", "pcf", "snf", "ttf", "pfa", "arc", "gca", "ulx", "gnumeric", "gramps", "gtar", "gz", "hdf", "php", "install", "ins", "isp", "iii", "ipa", "ipg", "ipsw", "ite", "itlp", "itms", "itpc", "class", "jardiff", "jnlp", "skp", "latex", "luac", "lha", "run", "mie", "mvc", "mxp", "mobi", "prc", "application", "slupkg-ms", "manifest", "lit", "lnk", "vsto", "wmd", "wmz", "xbap", "mdb", "obd", "crd", "clp", "bat", "com", "dll", "m13", "m14", "mvb", "emf", "wmf", "mny", "pub", "scd", "trm", "wri", "nc", "pac", "nzb", "hhc", "parquet", "pma", "pmc", "pml", "pmr", "pmw", "pl", "pm", "p12", "pfx", "p7b", "spc", "p7r", "pcast", "qtl", "rar", "ris", "safariextz", "webarchive", "sgimb", "sh", "shar", "mfp", "swf", "xap", "mmf", "sql", "sit", "sitx", "srt", "sv4cpio", "sv4crc", "t3", "gam", "tar", "tcl", "tk", "tex", "tfm", "texi", "texinfo", "obj", "roff", "t", "tr", "man", "me", "ms", "ustar", "hdd", "ova", "ovf", "vbox", "vbox-extpack", "vdi", "vhd", "vmdk", "src", "webapp", "wlpginstall", "wlpginstall3", "cer", "crt", "der", "pem", "fig", "xlf", "xpi", "xz", "z1", "z2", "z3", "z4", "z5", "z6", "z7", "z8", "xaml", "xdf", "xenc", "xht", "xhtml", "asa", "asax", "ascx", "ashx", "asmx", "aspx", "config", "coverage", "datasource", "dgml", "filters", "generictest", "hxa", "hxc", "hxe", "hxf", "hxk", "hxv", "loadtest", "master", "mtx", "orderedtest", "psess", "rdlc", "resx", "rng", "ruleset", "settings", "sitemap", "skin", "snippet", "svc", "testrunconfig", "testsettings", "trx", "vcproj", "vcxproj", "vscontent", "vsmdi", "webtest", "wiq", "xmta", "xsc", "xss", "xop", "xpl", "xspf", "mxml", "xhvml", "xvm", "xvml", "yang", "yin", "zip", "aac", "adts", "ac3", "aif", "aifc", "aiff", "cdda", "axa", "aa", "au", "snd", "flac", "m4a", "m4b", "m4p", "mid", "midi", "rmi", "kar", "mp4a", "m2a", "m3a", "mp2", "mp2a", "mp3", "mpga", "oga", "ogg", "opus", "spx", "s3m", "pls", "sil", "aax", "uva", "uvva", "eol", "adt", "dra", "dts", "dtshd", "lvp", "pya", "ecelp4800", "ecelp7470", "ecelp9600", "rip", "wav", "wave", "weba", "caf", "gsm", "m4r", "mka", "m3u", "m3u8", "wax", "wma", "ra", "ram", "rpm", "sd2", "smd", "smx", "smz", "xm", "cdx", "cif", "cmdf", "cml", "csml", "xyz", "dwf", "ttc", "ttf", "woff2", "apng", "avif", "avifs", "bmp", "dib", "cgm", "cod", "g3", "gif", "heic", "heics", "heif", "heifs", "ief", "jp2", "jpg2", "jfif", "jpe", "jpeg", "jpg", "jpm", "jpf", "jpx", "jxl", "ktx", "pct", "pic", "pict", "png", "pnz", "btif", "sgi", "svg", "svgz", "tif", "tiff", "uvg", "uvi", "uvvg", "uvvi", "djv", "djvu", "fbs", "fpx", "fst", "mmr", "rlc", "mdi", "wdp", "npx", "hdr", "rf", "wbmp", "xif", "webp", "3ds", "dng", "cr2", "cr3", "crw", "ras", "cmx", "erf", "fh", "fh4", "fh5", "fh7", "fhc", "raf", "ico", "art", "jng", "dcr", "k25", "kdc", "mac", "pnt", "pntg", "mrw", "sid", "nef", "nrw", "orf", "raw", "rw2", "rwl", "pef", "pnm", "pbm", "pgm", "ppm", "qti", "qtif", "rgb", "x3f", "arw", "sr2", "srf", "tga", "xbm", "xpm", "xwd", "disposition-notification", "u8msg", "u8dsn", "u8mdn", "u8hdr", "eml", "mht", "mhtml", "mime", "nws", "gltf", "glb", "iges", "igs", "mesh", "msh", "silo", "dae", "gdl", "gtw", "vtu", "vrml", "x3db", "x3dbz", "x3dv", "x3dvz", "x3d", "x3dz", "appcache", "ics", "ifb", "coffee", "litcoffee", "css", "csv", "dlm", "gemini", "gmi", "323", "htm", "html", "hxt", "shtml", "uls", "jade", "ecma", "es", "js", "jsm", "jsx", "less", "markdown", "md", "mml", "n3", "asm", "bas", "c", "cc", "cd", "cfg", "cmd", "cnf", "conf", "cpp", "cs", "csdproj", "csproj", "cxx", "dbproj", "def", "dsw", "h", "hh", "hpp", "hxx", "i", "idl", "in", "inc", "ini", "inl", "ipproj", "jsxbin", "list", "log", "lst", "mak", "map", "mk", "odh", "odl", "pkgdef", "pkgundef", "py", "rc", "rc2", "rct", "reg", "rgs", "s", "scr", "sln", "sol", "sor", "srf", "text", "tlh", "tli", "txt", "user", "vb", "vbdproj", "vbproj", "vcs", "vddproj", "vdp", "vdproj", "vspscc", "vsscc", "vssscc", "xdr", "xoml", "dsc", "rtx", "sct", "wsc", "sgm", "sgml", "shex", "slim", "slm", "styl", "stylus", "tsv", "ttl", "uri", "uris", "urls", "vbs", "vcard", "curl", "dcurl", "mcurl", "scurl", "sub", "fly", "flx", "gv", "3dml", "spot", "jad", "wml", "wmls", "vtt", "htt", "dic", "htc", "f", "f77", "f90", "for", "feature", "hbs", "hdml", "qht", "qhtm", "lua", "md", "mkd", "contact", "group", "iqy", "rqy", "nfo", "opml", "p", "pas", "pde", "rs", "sass", "scss", "etx", "sfv", "ymp", "toml", "uu", "vcf", "yaml", "yml", "addin", "disco", "dll.config", "dtd", "dtsconfig", "exe.config", "mno", "ssisdeploymentmanifest", "vml", "vsct", "vsixlangpack", "vsixmanifest", "vssettings", "vstemplate", "wsdl", "xml", "xrm-ms", "xsd", "xsf", "xsl", "xslt", "3gp", "3gpp", "3g2", "3gp2", "axv", "divx", "h261", "h263", "h264", "jpgv", "jpgm", "mj2", "mjp2", "mp4", "mp4v", "mpg4", "m1v", "m2v", "mod", "mp2", "mp2v", "mpa", "mpe", "mpeg", "mpg", "mpv2", "vbk", "ogv", "mov", "mqv", "qt", "uvh", "uvvh", "uvm", "uvvm", "uvp", "uvvp", "uvs", "uvvs", "uvv", "uvvv", "m2t", "m2ts", "mts", "ts", "tts", "dvb", "fvt", "m4u", "mxu", "pyv", "uvu", "uvvu", "viv", "webm", "dif", "dv", "f4v", "fli", "flv", "ivf", "lsf", "lsx", "m4v", "mk3d", "mks", "mkv", "mng", "asf", "asr", "asx", "nsc", "vob", "wm", "wmp", "wmv", "wmx", "wvx", "avi", "movie", "smv", "ice", "flr", "wrl", "wrz", "xaf", "xof"];
