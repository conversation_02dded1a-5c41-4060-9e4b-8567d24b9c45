{"rustc": 15497389221046826682, "features": "[\"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "declared_features": "[\"arbitrary\", \"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "target": 15092862010867938684, "profile": 8276155916380437441, "path": 1345994406996652458, "deps": [[1042707345065476716, "tinyvec", false, 14395214977053751095], [3016319839805820069, "ring", false, 13493840211025283393], [6955678925937229351, "slab", false, 15809420321409050931], [8008191657135824715, "thiserror", false, 9973082643837402726], [8606274917505247608, "tracing", false, 8513811094196095045], [11295624341523567602, "rustls", false, 16160104221745040905], [13208667028893622512, "rand", false, 6475633719892245940], [14394652928131349565, "rustls_native_certs", false, 9979925801353901611], [16055916053474393816, "rustc_hash", false, 16088151904853800436], [16066129441945555748, "bytes", false, 1974571504047377931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/quinn-proto-9c6433a56c548c95/dep-lib-quinn_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}