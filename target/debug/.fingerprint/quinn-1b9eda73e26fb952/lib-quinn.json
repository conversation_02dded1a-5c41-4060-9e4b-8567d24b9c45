{"rustc": 15497389221046826682, "features": "[\"default\", \"log\", \"native-certs\", \"ring\", \"runtime-tokio\", \"rustls\", \"tls-rustls\"]", "declared_features": "[\"async-io\", \"async-std\", \"default\", \"futures-io\", \"lock_tracking\", \"log\", \"native-certs\", \"ring\", \"runtime-async-std\", \"runtime-tokio\", \"rustls\", \"tls-rustls\"]", "target": 15407875845472914810, "profile": 8276155916380437441, "path": 5503033928563799346, "deps": [[992440926759950020, "udp", false, 15120239278420286784], [1906322745568073236, "pin_project_lite", false, 7773456665029720526], [8008191657135824715, "thiserror", false, 9973082643837402726], [8606274917505247608, "tracing", false, 8513811094196095045], [9538054652646069845, "tokio", false, 15865268293722207714], [11295624341523567602, "rustls", false, 16160104221745040905], [16055916053474393816, "rustc_hash", false, 16088151904853800436], [16066129441945555748, "bytes", false, 1974571504047377931], [18009697307577877652, "proto", false, 12028749046139034012]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/quinn-1b9eda73e26fb952/dep-lib-quinn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}