{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 5347358027863023418, "path": 666726909874963799, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bs58-e573dd656e11b398/dep-lib-bs58", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}