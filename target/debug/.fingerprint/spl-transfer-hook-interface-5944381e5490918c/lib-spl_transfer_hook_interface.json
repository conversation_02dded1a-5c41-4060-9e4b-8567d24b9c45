{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 8276155916380437441, "path": 4116893179673126486, "deps": [[*****************, "solana_program", false, 4986504109236538142], [7479230097745373958, "spl_type_length_value", false, 13382843951692999829], [9529943735784919782, "arrayref", false, 10826664316956858232], [11699822774991256268, "spl_pod", false, 18134339964564946107], [12414676574469740085, "spl_tlv_account_resolution", false, 4906306801256307528], [14074610438553418890, "bytemuck", false, 14823366590196015512], [14673743079976092479, "spl_program_error", false, 12121062696453503310], [18269786033916185670, "spl_discriminator", false, 5341576822474742393]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-transfer-hook-interface-5944381e5490918c/dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}