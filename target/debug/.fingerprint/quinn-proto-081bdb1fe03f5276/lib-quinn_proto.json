{"rustc": 15497389221046826682, "features": "[\"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "declared_features": "[\"arbitrary\", \"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "target": 15092862010867938684, "profile": 5347358027863023418, "path": 1345994406996652458, "deps": [[1042707345065476716, "tinyvec", false, 18357588524411641920], [3016319839805820069, "ring", false, 2444023349581459460], [6955678925937229351, "slab", false, 9352509689049892774], [8008191657135824715, "thiserror", false, 647754102121251511], [8606274917505247608, "tracing", false, 16533887323355218068], [11295624341523567602, "rustls", false, 15971944155683504410], [13208667028893622512, "rand", false, 10799981918500864721], [14394652928131349565, "rustls_native_certs", false, 5405244081442724998], [16055916053474393816, "rustc_hash", false, 7447400577716921883], [16066129441945555748, "bytes", false, 16337691178649358567]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/quinn-proto-081bdb1fe03f5276/dep-lib-quinn_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}