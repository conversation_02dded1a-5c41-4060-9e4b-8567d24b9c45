{"rustc": 15497389221046826682, "features": "[\"ansi_term\", \"atty\", \"color\", \"default\", \"strsim\", \"suggestions\", \"vec_map\"]", "declared_features": "[\"ansi_term\", \"atty\", \"clippy\", \"color\", \"debug\", \"default\", \"doc\", \"nightly\", \"no_cargo\", \"strsim\", \"suggestions\", \"term_size\", \"unstable\", \"vec_map\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 12198692761336931930, "profile": 8276155916380437441, "path": 3553662039149755952, "deps": [[1322514204948454048, "unicode_width", false, 17667421085076015143], [1810510990979880151, "ansi_term", false, 12747173518028086830], [6485010074357387197, "textwrap", false, 7738177905789152898], [10058577953979766589, "atty", false, 18230058386210979922], [10110425334065384495, "strsim", false, 9181689375225133821], [10435729446543529114, "bitflags", false, 17362181686205706553], [14451951854123638585, "vec_map", false, 7356805165136327036]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-24b030f7e9faa00c/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}