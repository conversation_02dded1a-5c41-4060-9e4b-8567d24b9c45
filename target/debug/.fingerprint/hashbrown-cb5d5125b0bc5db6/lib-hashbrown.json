{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 5347358027863023418, "path": 8979737143335350578, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-cb5d5125b0bc5db6/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}