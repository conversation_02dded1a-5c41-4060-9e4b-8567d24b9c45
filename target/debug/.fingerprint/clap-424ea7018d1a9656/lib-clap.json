{"rustc": 15497389221046826682, "features": "[\"atty\", \"cargo\", \"color\", \"default\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 5347358027863023418, "path": 12757537276637098162, "deps": [[580378868546634928, "textwrap", false, 10775870295709121938], [3722963349756955755, "once_cell", false, 9250329688931680279], [5841926810058920975, "strsim", false, 1474849749457155789], [10058577953979766589, "atty", false, 10475120217906139452], [10435729446543529114, "bitflags", false, 11608506283860663249], [12902659978838094914, "termcolor", false, 5584751137504140656], [14923790796823607459, "indexmap", false, 17538420343165443336], [15944592714770878610, "clap_lex", false, 11928959429920221736]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-424ea7018d1a9656/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}