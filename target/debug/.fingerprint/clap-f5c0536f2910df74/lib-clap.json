{"rustc": 15497389221046826682, "features": "[\"ansi_term\", \"atty\", \"color\", \"default\", \"strsim\", \"suggestions\", \"vec_map\"]", "declared_features": "[\"ansi_term\", \"atty\", \"clippy\", \"color\", \"debug\", \"default\", \"doc\", \"nightly\", \"no_cargo\", \"strsim\", \"suggestions\", \"term_size\", \"unstable\", \"vec_map\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 12198692761336931930, "profile": 5347358027863023418, "path": 3553662039149755952, "deps": [[1322514204948454048, "unicode_width", false, 3288189350764178780], [1810510990979880151, "ansi_term", false, 2201312688828231458], [6485010074357387197, "textwrap", false, 12534286199477865720], [10058577953979766589, "atty", false, 10475120217906139452], [10110425334065384495, "strsim", false, 3824400124827442849], [10435729446543529114, "bitflags", false, 11608506283860663249], [14451951854123638585, "vec_map", false, 1614950392633731516]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-f5c0536f2910df74/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}