{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 5347358027863023418, "path": 4116893179673126486, "deps": [[*****************, "solana_program", false, 1645851893497941029], [7479230097745373958, "spl_type_length_value", false, 10336466407159560407], [9529943735784919782, "arrayref", false, 6526092932885921554], [11699822774991256268, "spl_pod", false, 2156292640326997969], [12414676574469740085, "spl_tlv_account_resolution", false, 8736517422910355414], [14074610438553418890, "bytemuck", false, 9182663254198208560], [14673743079976092479, "spl_program_error", false, 2722846332477545256], [18269786033916185670, "spl_discriminator", false, 18266981688409265468]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-transfer-hook-interface-a4771cc8eb27c0d3/dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}