{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 5347358027863023418, "path": 5810756262404464549, "deps": [[2317793503723491507, "untrusted", false, 14158143980026264301], [3016319839805820069, "build_script_build", false, 12541619129441019571]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-97897f396b24b120/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}