{"rustc": 15497389221046826682, "features": "[\"atty\", \"cargo\", \"color\", \"default\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 8276155916380437441, "path": 12757537276637098162, "deps": [[580378868546634928, "textwrap", false, 17156737773789466695], [3722963349756955755, "once_cell", false, 3950140573650441576], [5841926810058920975, "strsim", false, 10277112379213334398], [10058577953979766589, "atty", false, 18230058386210979922], [10435729446543529114, "bitflags", false, 17362181686205706553], [12902659978838094914, "termcolor", false, 10670547349733107992], [14923790796823607459, "indexmap", false, 8472400635972415033], [15944592714770878610, "clap_lex", false, 7288571212156899254]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-f75b183a234ba9be/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}